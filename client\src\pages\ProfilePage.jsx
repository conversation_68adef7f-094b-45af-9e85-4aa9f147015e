import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { message, Tabs, Spin, Modal, Upload } from 'antd';
import { 
  UserOutlined, 
  EnvironmentOutlined, 
  BellOutlined, 
  SafetyCertificateOutlined, 
  SettingOutlined, 
  CameraOutlined, 
  UploadOutlined, 
  CloseOutlined, 
  CheckOutlined, 
  CreditCardOutlined, 
  ShoppingOutlined, 
  HeartOutlined
} from '@ant-design/icons';
import ProfileHeader from '../components/profile/ProfileHeader';
import PersonalInfoSection from '../components/profile/PersonalInfoSection';
import AddressInfoSection from '../components/profile/AddressInfoSection';

const { TabPane } = Tabs;

const ProfilePage = () => {
  const { user, isAuthenticated, userType, updateProfile, isLoading } = useAuth();
  const navigate = useNavigate();
  
  // State for edit mode
  const [isEditing, setIsEditing] = useState(false);
  
  // State for form data
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    bio: '',
    displayName: '',
    website: '',
    preferences: {
      notifications: {
        email: true,
        sms: false,
        push: true
      },
      language: 'en',
      currency: 'USD'
    }
  });
  
  // State for profile picture
  const [avatar, setAvatar] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  
  // State for saving
  const [saving, setSaving] = useState(false);
  
  // State for active tab
  const [activeTab, setActiveTab] = useState('1');

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/auth');
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '',
        dateOfBirth: user.dateOfBirth || '',
        gender: user.gender || '',
        address: user.address || '',
        city: user.city || '',
        state: user.state || '',
        zipCode: user.zipCode || '',
        country: user.country || 'United States',
        bio: user.bio || '',
        displayName: user.displayName || '',
        website: user.website || '',
        preferences: user.preferences || {
          notifications: {
            email: true,
            sms: false,
            push: true
          },
          language: 'en',
          currency: 'USD'
        }
      });
      
      // Set avatar preview if user has an avatar
      if (user.avatar) {
        setAvatarPreview(user.avatar);
      }
    }
  }, [user]);

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();
    setSaving(true);
    
    try {
      // Create form data for file upload
      const profileData = new FormData();
      
      // Add all form fields to form data
      Object.keys(formData).forEach(key => {
        if (key !== 'preferences') {
          profileData.append(key, formData[key]);
        }
      });
      
      // Add preferences as JSON string
      profileData.append('preferences', JSON.stringify(formData.preferences));
      
      // Add avatar if changed
      if (avatar) {
        profileData.append('avatar', avatar);
      }
      
      const result = await updateProfile(profileData);
      
      if (result.success) {
        message.success('Profile updated successfully!');
        setIsEditing(false);
        setAvatar(null); // Reset avatar state after successful update
      } else {
        message.error(result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Update error:', error);
      message.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (field, value) => {
    if (field.includes('.')) {
      // Handle nested fields (e.g., preferences.notifications.email)
      const fields = field.split('.');
      setFormData(prev => {
        const newData = { ...prev };
        let current = newData;
        
        for (let i = 0; i < fields.length - 1; i++) {
          current = current[fields[i]];
        }
        
        current[fields[fields.length - 1]] = value;
        return newData;
      });
    } else {
      // Handle top-level fields
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };
  
  const handleAvatarChange = (info) => {
    if (info.file) {
      // Check file size (limit to 2MB)
      if (info.file.size > 2 * 1024 * 1024) {
        message.error('Image must be smaller than 2MB!');
        return;
      }
      
      // Check file type
      if (!['image/jpeg', 'image/png', 'image/gif'].includes(info.file.type)) {
        message.error('You can only upload JPG, PNG, or GIF files!');
        return;
      }
      
      // Set avatar file
      setAvatar(info.file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(info.file);
      
      // Close modal
      setShowAvatarModal(false);
    }
  };
  
  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    
    // If canceling edit, reset form data to user data
    if (isEditing) {
      if (user) {
        setFormData({
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          phone: user.phone || '',
          dateOfBirth: user.dateOfBirth || '',
          gender: user.gender || '',
          address: user.address || '',
          city: user.city || '',
          state: user.state || '',
          zipCode: user.zipCode || '',
          country: user.country || 'United States',
          bio: user.bio || '',
          displayName: user.displayName || '',
          website: user.website || '',
          preferences: user.preferences || {
            notifications: {
              email: true,
              sms: false,
              push: true
            },
            language: 'en',
            currency: 'USD'
          }
        });
        
        // Reset avatar preview
        if (user.avatar) {
          setAvatarPreview(user.avatar);
        } else {
          setAvatarPreview(null);
        }
        
        // Reset avatar
        setAvatar(null);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-5xl mx-auto px-4">
        {/* Profile Header with Avatar */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
            <div className="relative">
              <div className="h-32 w-32 rounded-full border-4 border-gray-200 bg-white shadow-md overflow-hidden">
                {avatarPreview ? (
                  <img 
                    src={avatarPreview} 
                    alt="Profile" 
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gray-100">
                    <UserOutlined className="text-6xl text-gray-400" />
                  </div>
                )}
              </div>
              
              {isEditing && (
                <button 
                  onClick={() => setShowAvatarModal(true)}
                  className="absolute bottom-0 right-0 bg-orange-500 text-white p-2 rounded-full shadow-md hover:bg-orange-600 transition-colors duration-200"
                >
                  <CameraOutlined />
                </button>
              )}
            </div>
            
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {user.userType === 'vendor' ? (
                      user.businessName || user.contactPerson || 'Vendor Account'
                    ) : (
                      `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'User Account'
                    )}
                  </h1>
                  <p className="text-gray-500">{user.email}</p>
                  <div className="flex items-center mt-2 text-sm text-gray-500">
                    <SafetyCertificateOutlined className="mr-1" />
                    <span className="capitalize">{userType} Account</span>
                    {user.emailVerification?.isVerified && (
                      <>
                        <span className="mx-2">•</span>
                        <span className="text-green-600">✓ Verified</span>
                      </>
                    )}
                    <span className="mx-2">•</span>
                    <span>Member since {new Date(user.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
                
                <div className="flex space-x-3 mt-4 sm:mt-0">
                  {!isEditing ? (
                    <button
                      onClick={handleEditToggle}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2"
                    >
                      <SettingOutlined />
                      <span>Edit Profile</span>
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={handleEditToggle}
                        disabled={saving}
                        className="border border-gray-300 hover:bg-gray-100 text-gray-700 px-4 py-2 rounded-md transition-colors duration-200 disabled:opacity-50"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleSubmit}
                        disabled={saving}
                        className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
                      >
                        {saving ? (
                          <>
                            <Spin size="small" />
                            <span>Saving...</span>
                          </>
                        ) : (
                          <>
                            <CheckOutlined />
                            <span>Save Changes</span>
                          </>
                        )}
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Profile Content */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <Tabs 
            activeKey={activeTab} 
            onChange={setActiveTab}
            className="profile-tabs"
            tabBarStyle={{ padding: '0 24px', borderBottom: '1px solid #f0f0f0' }}
          >
            <TabPane 
              tab={
                <span className="flex items-center space-x-2 py-4">
                  <UserOutlined />
                  <span>Personal Info</span>
                </span>
              } 
              key="1"
            >
              <div className="p-6">
                <PersonalInfoSection 
                  user={user} 
                  userType={userType} 
                  editData={formData} 
                  isEditing={isEditing} 
                  onInputChange={handleChange} 
                />
                
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">About Me</h3>
                  {isEditing ? (
                    <textarea
                      value={formData.bio || ''}
                      onChange={(e) => handleChange('bio', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                      placeholder="Tell us about yourself..."
                      rows={4}
                    />
                  ) : (
                    <p className="text-gray-700">
                      {user.bio || 'No bio provided yet.'}
                    </p>
                  )}
                </div>
                
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Social Profiles</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Display Name
                      </label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={formData.displayName || ''}
                          onChange={(e) => handleChange('displayName', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                          placeholder="Enter display name"
                        />
                      ) : (
                        <p className="text-gray-700">
                          {user.displayName || 'Not provided'}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Website
                      </label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={formData.website || ''}
                          onChange={(e) => handleChange('website', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                          placeholder="Enter website URL"
                        />
                      ) : (
                        <p className="text-gray-700">
                          {user.website ? (
                            <a href={user.website} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                              {user.website}
                            </a>
                          ) : (
                            'Not provided'
                          )}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabPane>
            
            <TabPane 
              tab={
                <span className="flex items-center space-x-2 py-4">
                  <EnvironmentOutlined />
                  <span>Address</span>
                </span>
              } 
              key="2"
            >
              <div className="p-6">
                <AddressInfoSection 
                  user={user} 
                  editData={formData} 
                  isEditing={isEditing} 
                  onInputChange={handleChange} 
                />
              </div>
            </TabPane>
            
            <TabPane 
              tab={
                <span className="flex items-center space-x-2 py-4">
                  <BellOutlined />
                  <span>Preferences</span>
                </span>
              } 
              key="3"
            >
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-800">Email Notifications</h4>
                      <p className="text-sm text-gray-500">Receive order updates and promotions via email</p>
                    </div>
                    <div className="relative">
                      <input
                        type="checkbox"
                        id="email-notifications"
                        checked={formData.preferences?.notifications?.email}
                        onChange={(e) => handleChange('preferences.notifications.email', e.target.checked)}
                        disabled={!isEditing}
                        className="sr-only"
                      />
                      <label
                        htmlFor="email-notifications"
                        className={`block w-14 h-7 rounded-full transition-colors duration-200 ${
                          formData.preferences?.notifications?.email ? 'bg-orange-500' : 'bg-gray-300'
                        } ${!isEditing ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        <span
                          className={`block w-5 h-5 mt-1 ml-1 bg-white rounded-full transition-transform duration-200 transform ${
                            formData.preferences?.notifications?.email ? 'translate-x-7' : ''
                          }`}
                        />
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-800">SMS Notifications</h4>
                      <p className="text-sm text-gray-500">Receive order updates and promotions via SMS</p>
                    </div>
                    <div className="relative">
                      <input
                        type="checkbox"
                        id="sms-notifications"
                        checked={formData.preferences?.notifications?.sms}
                        onChange={(e) => handleChange('preferences.notifications.sms', e.target.checked)}
                        disabled={!isEditing}
                        className="sr-only"
                      />
                      <label
                        htmlFor="sms-notifications"
                        className={`block w-14 h-7 rounded-full transition-colors duration-200 ${
                          formData.preferences?.notifications?.sms ? 'bg-orange-500' : 'bg-gray-300'
                        } ${!isEditing ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        <span
                          className={`block w-5 h-5 mt-1 ml-1 bg-white rounded-full transition-transform duration-200 transform ${
                            formData.preferences?.notifications?.sms ? 'translate-x-7' : ''
                          }`}
                        />
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-800">Push Notifications</h4>
                      <p className="text-sm text-gray-500">Receive order updates and promotions via push notifications</p>
                    </div>
                    <div className="relative">
                      <input
                        type="checkbox"
                        id="push-notifications"
                        checked={formData.preferences?.notifications?.push}
                        onChange={(e) => handleChange('preferences.notifications.push', e.target.checked)}
                        disabled={!isEditing}
                        className="sr-only"
                      />
                      <label
                        htmlFor="push-notifications"
                        className={`block w-14 h-7 rounded-full transition-colors duration-200 ${
                          formData.preferences?.notifications?.push ? 'bg-orange-500' : 'bg-gray-300'
                        } ${!isEditing ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        <span
                          className={`block w-5 h-5 mt-1 ml-1 bg-white rounded-full transition-transform duration-200 transform ${
                            formData.preferences?.notifications?.push ? 'translate-x-7' : ''
                          }`}
                        />
                      </label>
                    </div>
                  </div>
                </div>
                
                <h3 className="text-lg font-medium text-gray-900 mt-8 mb-4">Language & Currency</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Language
                    </label>
                    <select
                      value={formData.preferences?.language || 'en'}
                      onChange={(e) => handleChange('preferences.language', e.target.value)}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 ${!isEditing ? 'bg-gray-100' : ''}`}
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                      <option value="zh">Chinese</option>
                      <option value="ja">Japanese</option>
                      <option value="hi">Hindi</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency
                    </label>
                    <select
                      value={formData.preferences?.currency || 'USD'}
                      onChange={(e) => handleChange('preferences.currency', e.target.value)}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 ${!isEditing ? 'bg-gray-100' : ''}`}
                    >
                      <option value="USD">US Dollar ($)</option>
                      <option value="EUR">Euro (€)</option>
                      <option value="GBP">British Pound (£)</option>
                      <option value="JPY">Japanese Yen (¥)</option>
                      <option value="CAD">Canadian Dollar (C$)</option>
                      <option value="AUD">Australian Dollar (A$)</option>
                      <option value="INR">Indian Rupee (₹)</option>
                    </select>
                  </div>
                </div>
              </div>
            </TabPane>
            
            <TabPane 
              tab={
                <span className="flex items-center space-x-2 py-4">
                  <ShoppingOutlined />
                  <span>Orders</span>
                </span>
              } 
              key="4"
            >
              <div className="p-6">
                <div className="text-center py-8">
                  <ShoppingOutlined className="text-6xl text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Your Order History</h3>
                  <p className="text-gray-500 mb-4">View and track all your orders in one place</p>
                  <button 
                    onClick={() => navigate('/orders')}
                    className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200"
                  >
                    View Orders
                  </button>
                </div>
              </div>
            </TabPane>
            
            <TabPane 
              tab={
                <span className="flex items-center space-x-2 py-4">
                  <HeartOutlined />
                  <span>Wishlist</span>
                </span>
              } 
              key="5"
            >
              <div className="p-6">
                <div className="text-center py-8">
                  <HeartOutlined className="text-6xl text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Your Wishlist</h3>
                  <p className="text-gray-500 mb-4">Products you've saved for later</p>
                  <button 
                    onClick={() => navigate('/wishlist')}
                    className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200"
                  >
                    View Wishlist
                  </button>
                </div>
              </div>
            </TabPane>
          </Tabs>
        </div>
      </div>
      
      {/* Avatar Upload Modal */}
      <Modal
        title="Update Profile Picture"
        open={showAvatarModal}
        onCancel={() => setShowAvatarModal(false)}
        footer={null}
      >
        <div className="py-4">
          <div className="text-center mb-6">
            <div className="h-32 w-32 mx-auto rounded-full border-4 border-gray-200 overflow-hidden">
              {avatarPreview ? (
                <img 
                  src={avatarPreview} 
                  alt="Profile Preview" 
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-gray-100">
                  <UserOutlined className="text-6xl text-gray-400" />
                </div>
              )}
            </div>
          </div>
          
          <Upload
            name="avatar"
            listType="picture"
            showUploadList={false}
            beforeUpload={(file) => {
              handleAvatarChange({ file });
              return false;
            }}
          >
            <div className="text-center">
              <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2 mx-auto">
                <UploadOutlined />
                <span>Upload New Picture</span>
              </button>
              <p className="text-xs text-gray-500 mt-2">
                JPG, PNG or GIF. Max size 2MB.
              </p>
            </div>
          </Upload>
          
          {avatarPreview && (
            <div className="mt-4 text-center">
              <button 
                onClick={() => {
                  setAvatarPreview(null);
                  setAvatar(null);
                  setShowAvatarModal(false);
                }}
                className="text-red-500 hover:text-red-700 transition-colors duration-200 flex items-center space-x-1 mx-auto"
              >
                <CloseOutlined />
                <span>Remove Picture</span>
              </button>
            </div>
          )}
        </div>
      </Modal>
      
      {/* Add custom styles for the profile page */}
      <style jsx>{`
        .profile-tabs .ant-tabs-nav {
          margin-bottom: 0;
        }
        
        .profile-tabs .ant-tabs-tab {
          padding: 12px 0;
          margin: 0 16px 0 0;
        }
        
        .profile-tabs .ant-tabs-tab-active {
          font-weight: 500;
        }
        
        .profile-tabs .ant-tabs-ink-bar {
          background-color: #f97316;
          height: 3px;
        }
      `}</style>
    </div>
  );
};

export default ProfilePage;