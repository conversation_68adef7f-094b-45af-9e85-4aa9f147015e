
#!/usr/bin/env node

require('dotenv').config();
const readline = require('readline');
const mongoose = require('mongoose');
const User = require('./server/schema/userSchema');

// Colors for terminal output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Function to format and display user data
function displayUserDetails(user) {
    if (!user) {
        console.log(`${colors.red}❌ User not found${colors.reset}\n`);
        return;
    }

    console.log(`\n${colors.bright}${colors.cyan}=== USER DETAILS ===${colors.reset}\n`);

    // Basic Information
    console.log(`${colors.bright}📋 Basic Information:${colors.reset}`);
    console.log(`   ID: ${colors.yellow}${user._id}${colors.reset}`);
    console.log(`   Email: ${colors.green}${user.email}${colors.reset}`);
    console.log(`   User Type: ${colors.blue}${user.userType}${colors.reset}`);
    console.log(`   Role: ${colors.blue}${user.role}${colors.reset}`);
    
    if (user.userType === 'user') {
        console.log(`   Name: ${colors.white}${user.firstName} ${user.lastName}${colors.reset}`);
    } else if (user.userType === 'vendor') {
        console.log(`   Business Name: ${colors.white}${user.businessName}${colors.reset}`);
        console.log(`   Business Type: ${colors.white}${user.businessType}${colors.reset}`);
        console.log(`   Contact Person: ${colors.white}${user.contactPerson}${colors.reset}`);
    }

    // Account Status
    console.log(`\n${colors.bright}🔐 Account Status:${colors.reset}`);
    console.log(`   Active: ${user.isActive ? colors.green + '✅ Yes' : colors.red + '❌ No'}${colors.reset}`);
    console.log(`   Blocked: ${user.isBlocked ? colors.red + '🚫 Yes' : colors.green + '✅ No'}${colors.reset}`);
    if (user.blockReason) {
        console.log(`   Block Reason: ${colors.red}${user.blockReason}${colors.reset}`);
    }
    console.log(`   Email Verified: ${user.emailVerification.isVerified ? colors.green + '✅ Yes' : colors.yellow + '⏳ No'}${colors.reset}`);
    if (user.emailVerification.verifiedAt) {
        console.log(`   Verified At: ${colors.white}${new Date(user.emailVerification.verifiedAt).toLocaleString()}${colors.reset}`);
    }

    // Personal Information
    if (user.dateOfBirth || user.gender || user.address) {
        console.log(`\n${colors.bright}👤 Personal Information:${colors.reset}`);
        if (user.dateOfBirth) {
            console.log(`   Date of Birth: ${colors.white}${new Date(user.dateOfBirth).toLocaleDateString()}${colors.reset}`);
        }
        if (user.gender) {
            console.log(`   Gender: ${colors.white}${user.gender}${colors.reset}`);
        }
        if (user.address) {
            console.log(`   Address: ${colors.white}${user.address}${colors.reset}`);
            if (user.city) console.log(`   City: ${colors.white}${user.city}${colors.reset}`);
            if (user.state) console.log(`   State: ${colors.white}${user.state}${colors.reset}`);
            if (user.zipCode) console.log(`   ZIP Code: ${colors.white}${user.zipCode}${colors.reset}`);
            if (user.country) console.log(`   Country: ${colors.white}${user.country}${colors.reset}`);
        }
    }

    // Vendor-specific information
    if (user.userType === 'vendor') {
        console.log(`\n${colors.bright}🏢 Vendor Information:${colors.reset}`);
        console.log(`   Approved: ${user.vendorStatus.isApproved ? colors.green + '✅ Yes' : colors.yellow + '⏳ Pending'}${colors.reset}`);
        if (user.vendorStatus.approvedAt) {
            console.log(`   Approved At: ${colors.white}${new Date(user.vendorStatus.approvedAt).toLocaleString()}${colors.reset}`);
        }
        if (user.vendorStatus.rejectionReason) {
            console.log(`   Rejection Reason: ${colors.red}${user.vendorStatus.rejectionReason}${colors.reset}`);
        }
        if (user.taxId) {
            console.log(`   Tax ID: ${colors.white}${user.taxId}${colors.reset}`);
        }
        if (user.businessAddress && Object.keys(user.businessAddress).length > 0) {
            console.log(`   Business Address:`);
            if (user.businessAddress.street) console.log(`     Street: ${colors.white}${user.businessAddress.street}${colors.reset}`);
            if (user.businessAddress.city) console.log(`     City: ${colors.white}${user.businessAddress.city}${colors.reset}`);
            if (user.businessAddress.state) console.log(`     State: ${colors.white}${user.businessAddress.state}${colors.reset}`);
            if (user.businessAddress.zipCode) console.log(`     ZIP: ${colors.white}${user.businessAddress.zipCode}${colors.reset}`);
            if (user.businessAddress.country) console.log(`     Country: ${colors.white}${user.businessAddress.country}${colors.reset}`);
        }
    }

    // Statistics
    if (user.statistics && (user.statistics.totalOrders > 0 || user.statistics.totalSpent > 0)) {
        console.log(`\n${colors.bright}📊 Statistics:${colors.reset}`);
        console.log(`   Total Orders: ${colors.yellow}${user.statistics.totalOrders}${colors.reset}`);
        console.log(`   Total Spent: ${colors.green}$${user.statistics.totalSpent.toFixed(2)}${colors.reset}`);
        console.log(`   Total Saved: ${colors.green}$${user.statistics.totalSaved.toFixed(2)}${colors.reset}`);
        console.log(`   Average Order Value: ${colors.green}$${user.statistics.averageOrderValue.toFixed(2)}${colors.reset}`);
        if (user.statistics.lastOrderDate) {
            console.log(`   Last Order: ${colors.white}${new Date(user.statistics.lastOrderDate).toLocaleString()}${colors.reset}`);
        }
    }

    // Security Information
    console.log(`\n${colors.bright}🔒 Security Information:${colors.reset}`);
    if (user.security.lastLogin) {
        console.log(`   Last Login: ${colors.white}${new Date(user.security.lastLogin).toLocaleString()}${colors.reset}`);
    }
    if (user.security.lastLoginIP) {
        console.log(`   Last Login IP: ${colors.white}${user.security.lastLoginIP}${colors.reset}`);
    }
    console.log(`   Login Attempts: ${colors.yellow}${user.security.loginAttempts}${colors.reset}`);
    console.log(`   Account Locked: ${user.isLocked ? colors.red + '🔒 Yes' : colors.green + '✅ No'}${colors.reset}`);
    if (user.security.passwordChangedAt) {
        console.log(`   Password Changed: ${colors.white}${new Date(user.security.passwordChangedAt).toLocaleString()}${colors.reset}`);
    }
    console.log(`   2FA Enabled: ${user.twoFactorAuth.isEnabled ? colors.green + '✅ Yes' : colors.yellow + '❌ No'}${colors.reset}`);

    // Preferences
    console.log(`\n${colors.bright}⚙️ Preferences:${colors.reset}`);
    console.log(`   Language: ${colors.white}${user.preferences.language}${colors.reset}`);
    console.log(`   Timezone: ${colors.white}${user.preferences.timezone}${colors.reset}`);
    console.log(`   Currency: ${colors.white}${user.preferences.currency}${colors.reset}`);
    
    // Notification preferences
    console.log(`   Notifications:`);
    console.log(`     Email Marketing: ${user.preferences.notifications.email.marketing ? colors.green + '✅' : colors.red + '❌'}${colors.reset}`);
    console.log(`     Email Orders: ${user.preferences.notifications.email.orderUpdates ? colors.green + '✅' : colors.red + '❌'}${colors.reset}`);
    console.log(`     SMS Marketing: ${user.preferences.notifications.sms.marketing ? colors.green + '✅' : colors.red + '❌'}${colors.reset}`);
    console.log(`     Push Notifications: ${user.preferences.notifications.push.marketing ? colors.green + '✅' : colors.red + '❌'}${colors.reset}`);

    // Social Authentication
    if (user.socialAuth.isEnabled && user.socialAuth.providers.length > 0) {
        console.log(`\n${colors.bright}🔗 Social Authentication:${colors.reset}`);
        user.socialAuth.providers.forEach(provider => {
            console.log(`   ${provider.provider}: ${colors.green}Connected${colors.reset} (${new Date(provider.connectedAt).toLocaleDateString()})`);
        });
    }

    // Timestamps
    console.log(`\n${colors.bright}📅 Timestamps:${colors.reset}`);
    console.log(`   Created: ${colors.white}${new Date(user.createdAt).toLocaleString()}${colors.reset}`);
    console.log(`   Updated: ${colors.white}${new Date(user.updatedAt).toLocaleString()}${colors.reset}`);
    console.log(`   Last Active: ${colors.white}${new Date(user.lastActiveAt).toLocaleString()}${colors.reset}`);

    console.log(`\n${colors.bright}${colors.cyan}=== END OF USER DETAILS ===${colors.reset}\n`);
}

// Function to connect to database
async function connectToDatabase() {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
        await mongoose.connect(mongoURI);
        console.log(`${colors.green}✅ Connected to MongoDB${colors.reset}`);
        return true;
    } catch (error) {
        console.error(`${colors.red}❌ Database connection failed:${colors.reset}`, error.message);
        return false;
    }
}

// Function to prompt for email
function promptForEmail() {
    return new Promise((resolve) => {
        rl.question(`${colors.bright}Enter user email: ${colors.reset}`, (email) => {
            resolve(email.trim().toLowerCase());
        });
    });
}

// Function to ask if user wants to search again
function askForAnother() {
    return new Promise((resolve) => {
        rl.question(`${colors.bright}Do you want to search for another user? (y/n): ${colors.reset}`, (answer) => {
            resolve(answer.toLowerCase().startsWith('y'));
        });
    });
}

// Main function
async function main() {
    console.log(`${colors.bright}${colors.blue}🔍 User Details Fetcher${colors.reset}\n`);
    
    // Connect to database
    const connected = await connectToDatabase();
    if (!connected) {
        process.exit(1);
    }

    try {
        let continueSearching = true;
        
        while (continueSearching) {
            // Get email from user
            const email = await promptForEmail();
            
            if (!email) {
                console.log(`${colors.red}❌ Please enter a valid email address${colors.reset}\n`);
                continue;
            }

            console.log(`\n${colors.yellow}🔍 Searching for user with email: ${email}${colors.reset}`);

            try {
                // Find user in database
                const user = await User.findByEmail(email);
                
                // Display user details
                displayUserDetails(user);
                
            } catch (error) {
                console.error(`${colors.red}❌ Error fetching user:${colors.reset}`, error.message);
            }

            // Ask if user wants to search again
            continueSearching = await askForAnother();
        }

    } catch (error) {
        console.error(`${colors.red}❌ An error occurred:${colors.reset}`, error.message);
    } finally {
        // Close connections
        rl.close();
        await mongoose.connection.close();
        console.log(`${colors.green}✅ Database connection closed${colors.reset}`);
        console.log(`${colors.bright}👋 Goodbye!${colors.reset}`);
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log(`\n${colors.yellow}⚠️ Process interrupted${colors.reset}`);
    rl.close();
    await mongoose.connection.close();
    process.exit(0);
});

// Run the script
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, displayUserDetails };