const User = require('../../models/User');
const imageUpload = require('../../middleware/upload/imageUpload');
const { validationResult } = require('express-validator');
const path = require('path');

/**
 * Get the current user's profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getProfile = async (req, res) => {
  try {
    // User is already attached to req by auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Return user data without sensitive fields
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Error fetching profile:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Update the current user's profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateProfile = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }
    
    // Get user from auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Fields that can be updated
    const allowedFields = [
      'firstName', 'lastName', 'phone', 'address', 'city', 
      'state', 'zipCode', 'country', 'dateOfBirth', 'gender',
      'bio', 'displayName', 'website'
    ];
    
    // Create update object with allowed fields
    const updateData = {};
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });
    
    // Handle preferences if provided
    if (req.body.preferences) {
      try {
        // If preferences is a string (from form data), parse it
        const preferences = typeof req.body.preferences === 'string'
          ? JSON.parse(req.body.preferences)
          : req.body.preferences;
        
        updateData.preferences = preferences;
      } catch (error) {
        console.error('Error parsing preferences:', error);
      }
    }
    
    // Handle avatar upload if provided
    if (req.file) {
      // If user already has an avatar, delete the old one
      if (user.avatar) {
        await imageUpload.deleteImage(user.avatar);
      }
      
      // Set new avatar URL
      updateData.avatar = req.fileUrl;
    }
    
    // Update user in database
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');
    
    res.status(200).json({
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Update the user's profile picture
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateProfilePicture = async (req, res) => {
  try {
    // Get user from auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No image provided'
      });
    }
    
    // If user already has an avatar, delete the old one
    if (user.avatar) {
      await imageUpload.deleteImage(user.avatar);
    }
    
    // Update user with new avatar URL
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      { $set: { avatar: req.fileUrl } },
      { new: true }
    ).select('-password');
    
    res.status(200).json({
      success: true,
      data: {
        avatar: updatedUser.avatar
      },
      message: 'Profile picture updated successfully'
    });
  } catch (error) {
    console.error('Error updating profile picture:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Delete the user's profile picture
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteProfilePicture = async (req, res) => {
  try {
    // Get user from auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Check if user has an avatar
    if (!user.avatar) {
      return res.status(400).json({
        success: false,
        error: 'No profile picture to delete'
      });
    }
    
    // Delete the avatar file
    await imageUpload.deleteImage(user.avatar);
    
    // Update user to remove avatar URL
    await User.findByIdAndUpdate(
      user._id,
      { $set: { avatar: null } }
    );
    
    res.status(200).json({
      success: true,
      message: 'Profile picture deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting profile picture:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Add a new address to the user's profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addAddress = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }
    
    // Get user from auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Create new address object
    const newAddress = {
      type: req.body.type || 'home',
      street: req.body.street,
      city: req.body.city,
      state: req.body.state,
      zipCode: req.body.zipCode,
      country: req.body.country,
      isDefault: req.body.isDefault || false
    };
    
    // If new address is default, update existing addresses
    if (newAddress.isDefault) {
      await User.updateOne(
        { _id: user._id, 'addresses.isDefault': true },
        { $set: { 'addresses.$.isDefault': false } }
      );
    }
    
    // Add new address to user
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      { $push: { addresses: newAddress } },
      { new: true }
    ).select('-password');
    
    res.status(200).json({
      success: true,
      data: updatedUser.addresses,
      message: 'Address added successfully'
    });
  } catch (error) {
    console.error('Error adding address:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Update an existing address
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateAddress = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }
    
    // Get user from auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Get address ID from params
    const { addressId } = req.params;
    
    // Check if address exists
    const addressExists = user.addresses.some(addr => addr._id.toString() === addressId);
    if (!addressExists) {
      return res.status(404).json({
        success: false,
        error: 'Address not found'
      });
    }
    
    // Create update object
    const updateFields = {};
    ['type', 'street', 'city', 'state', 'zipCode', 'country'].forEach(field => {
      if (req.body[field] !== undefined) {
        updateFields[`addresses.$.${field}`] = req.body[field];
      }
    });
    
    // Handle isDefault separately
    if (req.body.isDefault === true) {
      // First, set all addresses to non-default
      await User.updateOne(
        { _id: user._id, 'addresses.isDefault': true },
        { $set: { 'addresses.$.isDefault': false } }
      );
      
      // Then set this address as default
      updateFields['addresses.$.isDefault'] = true;
    }
    
    // Update the address
    await User.updateOne(
      { _id: user._id, 'addresses._id': addressId },
      { $set: updateFields }
    );
    
    // Get updated user
    const updatedUser = await User.findById(user._id).select('-password');
    
    res.status(200).json({
      success: true,
      data: updatedUser.addresses,
      message: 'Address updated successfully'
    });
  } catch (error) {
    console.error('Error updating address:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Delete an address
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteAddress = async (req, res) => {
  try {
    // Get user from auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Get address ID from params
    const { addressId } = req.params;
    
    // Check if address exists
    const address = user.addresses.find(addr => addr._id.toString() === addressId);
    if (!address) {
      return res.status(404).json({
        success: false,
        error: 'Address not found'
      });
    }
    
    // Remove address from user
    await User.updateOne(
      { _id: user._id },
      { $pull: { addresses: { _id: addressId } } }
    );
    
    // If deleted address was default and there are other addresses, set a new default
    if (address.isDefault && user.addresses.length > 1) {
      const remainingAddresses = user.addresses.filter(addr => addr._id.toString() !== addressId);
      await User.updateOne(
        { _id: user._id, 'addresses._id': remainingAddresses[0]._id },
        { $set: { 'addresses.$.isDefault': true } }
      );
    }
    
    // Get updated user
    const updatedUser = await User.findById(user._id).select('-password');
    
    res.status(200).json({
      success: true,
      data: updatedUser.addresses,
      message: 'Address deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting address:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Update user preferences
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updatePreferences = async (req, res) => {
  try {
    // Get user from auth middleware
    const user = req.user;
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Get preferences from request body
    const { preferences } = req.body;
    
    if (!preferences) {
      return res.status(400).json({
        success: false,
        error: 'No preferences provided'
      });
    }
    
    // Update user preferences
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      { $set: { preferences } },
      { new: true }
    ).select('-password');
    
    res.status(200).json({
      success: true,
      data: updatedUser.preferences,
      message: 'Preferences updated successfully'
    });
  } catch (error) {
    console.error('Error updating preferences:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};