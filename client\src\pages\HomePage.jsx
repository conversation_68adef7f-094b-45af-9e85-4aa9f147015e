import React from "react";
import CatergoriesMenu from "../components/ui/CaregoriesMenu";
import AutoScrollCarousel from "../components/ui/AutoScrollCarousel";
import ItemsCard from "../components/ItemsCard";
import ProductCards from "../components/ProductCards";
import Footer from "../components/Footer";
import Header from "../components/Header";
import { Card } from "antd";

const HomePage = () => {
  return (
    <>
      <Header />
      <div className="flex flex-col m-4 md:m-6 lg:m-8 bg-white md:flex-col lg:flex-row items-stretch justify-center gap-2 sm:gap-3 md:gap-4 w-full max-w-7xl mx-auto px-2 sm:px-4 md:px-6 py-2 sm:py-4">
        <div className="w-full md:w-auto lg:w-auto lg:flex-shrink-0 order-1 lg:order-1 max-w-full">
          <CatergoriesMenu />
        </div>
        
        <div className="w-full md:w-full lg:flex-1 order-2 lg:order-2 max-w-full overflow-hidden">
          <AutoScrollCarousel />
        </div>
        
        <div className="w-full md:w-auto lg:w-auto lg:flex-shrink-0 order-3 lg:order-3 max-w-full h-full">
          <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full border-none shadow-none bg-transparent h-full flex items-center">
            <img
              src="https://i.ibb.co/vCNSHPyr/k2-3a12d80f-4288-48d7-b7bd-d1b32da9fb21-v1.png"
              alt="Featured product"
              className="w-full h-auto"
              onError={(e) => {
                e.target.src = 'https://via.placeholder.com/300x400?text=Image+Not+Found';
              }}
            />
          </Card>
        </div>
      </div>

      <div className="bg-gray-50 py-6">
        <ItemsCard />
      </div>
      <div>
        <ProductCards />
      </div>
      <div>
        <Footer />
      </div>
    </>
  );
};

export default HomePage;