/**
 * Test the specific registration request that's failing
 */

require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testSpecificRegistration() {
    console.log('🔍 Testing the specific registration request that failed...\n');
    
    // This is the exact request data from the user's issue
    const registrationData = {
        "firstName": "John",
        "lastName": "Wick",
        "email": "<EMAIL>",
        "password": "Free@009",
        "userType": "user"
    };
    
    console.log('📋 Request Data:');
    console.log(JSON.stringify(registrationData, null, 2));
    console.log('\n🚀 Sending registration request...\n');
    
    try {
        const response = await axios.post(`${BASE_URL}/auth/register`, registrationData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ SUCCESS!');
        console.log(`Status: ${response.status}`);
        console.log(`Message: ${response.data.message}`);
        console.log(`User ID: ${response.data.data?.user?._id}`);
        console.log(`Email: ${response.data.data?.user?.email}`);
        console.log(`User Type: ${response.data.data?.user?.userType}`);
        
    } catch (error) {
        if (error.response) {
            console.log('❌ REGISTRATION FAILED');
            console.log(`Status: ${error.response.status}`);
            console.log(`Message: ${error.response.data.message}`);
            
            if (error.response.data.errors) {
                console.log('\n📝 Validation Errors:');
                error.response.data.errors.forEach(err => {
                    console.log(`- ${err.field}: ${err.message}`);
                });
            }
            
            // Check specific issues
            console.log('\n🔍 Analyzing the issue...');
            
            if (error.response.status === 400) {
                if (error.response.data.message.includes('already exists')) {
                    console.log('💡 ISSUE IDENTIFIED: Email already exists in database');
                    console.log('   Solution: Use a different email address or check if user is already registered');
                } else if (error.response.data.errors) {
                    console.log('💡 ISSUE IDENTIFIED: Validation errors');
                    console.log('   Check the validation errors above for specific field issues');
                } else {
                    console.log('💡 ISSUE: Unknown validation error');
                }
            }
            
        } else if (error.request) {
            console.log('❌ NETWORK ERROR');
            console.log('No response received from server');
            console.log('Make sure the server is running on http://localhost:5000');
        } else {
            console.log('❌ REQUEST ERROR');
            console.log('Error:', error.message);
        }
    }
    
    // Test password validation specifically
    console.log('\n🔐 Testing password validation...');
    const password = "Free@009";
    
    console.log(`Password: "${password}"`);
    console.log(`Length: ${password.length} (minimum 8) - ${password.length >= 8 ? '✅' : '❌'}`);
    console.log(`Has lowercase: ${/[a-z]/.test(password) ? '✅' : '❌'}`);
    console.log(`Has uppercase: ${/[A-Z]/.test(password) ? '✅' : '❌'}`);
    console.log(`Has number: ${/\d/.test(password) ? '✅' : '❌'}`);
    console.log(`Matches regex: ${/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password) ? '✅' : '❌'}`);
}

// Test with a fresh email to see if it's an existing user issue
async function testWithFreshEmail() {
    console.log('\n\n🆕 Testing with a fresh email address...\n');
    
    const timestamp = Date.now();
    const freshRegistrationData = {
        "firstName": "John",
        "lastName": "Wick",
        "email": `john.wick.${timestamp}@example.com`,
        "password": "Free@009",
        "userType": "user"
    };
    
    console.log('📋 Fresh Request Data:');
    console.log(JSON.stringify(freshRegistrationData, null, 2));
    console.log('\n🚀 Sending registration request...\n');
    
    try {
        const response = await axios.post(`${BASE_URL}/auth/register`, freshRegistrationData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ SUCCESS WITH FRESH EMAIL!');
        console.log(`Status: ${response.status}`);
        console.log(`Message: ${response.data.message}`);
        console.log('\n💡 CONCLUSION: The original email address was already registered');
        console.log('   The user should either:');
        console.log('   1. Use a different email address');
        console.log('   2. Try logging in with the existing account');
        console.log('   3. Use the forgot password feature if they forgot their password');
        
    } catch (error) {
        if (error.response) {
            console.log('❌ STILL FAILING WITH FRESH EMAIL');
            console.log(`Status: ${error.response.status}`);
            console.log(`Message: ${error.response.data.message}`);
            
            if (error.response.data.errors) {
                console.log('\n📝 Validation Errors:');
                error.response.data.errors.forEach(err => {
                    console.log(`- ${err.field}: ${err.message}`);
                });
            }
        }
    }
}

async function main() {
    await testSpecificRegistration();
    await testWithFreshEmail();
}

main().catch(console.error);