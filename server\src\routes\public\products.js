const express = require('express');
const router = express.Router();

// Import middleware
const { optionalAuth } = require('../../middleware/auth/authMiddleware');

// Import validation middleware
const { validateProductList, validateProductId } = require('../../middleware/validation/productValidation');

// Import controller
const {
  getProducts,
  getProduct,
  getFeaturedProducts,
  getNewArrivals,
  getBestSelling,
  getProductsByCategory,
  getProductsByVendor,
  searchProducts
} = require('../../controllers/public/productController');

/**
 * @route   GET /api/public/products/featured
 * @desc    Get featured products
 * @access  Public
 */
router.get('/featured', optionalAuth, getFeaturedProducts);

/**
 * @route   GET /api/public/products/new-arrivals
 * @desc    Get new arrival products
 * @access  Public
 */
router.get('/new-arrivals', optionalAuth, getNewArrivals);

/**
 * @route   GET /api/public/products/best-selling
 * @desc    Get best selling products
 * @access  Public
 */
router.get('/best-selling', optionalAuth, getBestSelling);

/**
 * @route   GET /api/public/products/search
 * @desc    Search products
 * @access  Public
 */
router.get('/search', optionalAuth, searchProducts);

/**
 * @route   GET /api/public/products/category/:categoryId
 * @desc    Get products by category
 * @access  Public
 */
router.get('/category/:categoryId', optionalAuth, getProductsByCategory);

/**
 * @route   GET /api/public/products/vendor/:vendorId
 * @desc    Get products by vendor
 * @access  Public
 */
router.get('/vendor/:vendorId', optionalAuth, getProductsByVendor);

/**
 * @route   GET /api/public/products
 * @desc    Get all active products with pagination and filters
 * @access  Public
 */
router.get('/', optionalAuth, validateProductList, getProducts);

/**
 * @route   GET /api/public/products/:id
 * @desc    Get single product by ID or slug
 * @access  Public
 */
router.get('/:id', optionalAuth, getProduct);

module.exports = router;