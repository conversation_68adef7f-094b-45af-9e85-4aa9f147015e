/**
 * Utility to clean up test users from the database
 * Run with: node cleanup-test-users.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./schema/userSchema');

const DB_URL = process.env.MONGODB_URI || process.env.DB_url;

async function connectDB() {
    try {
        await mongoose.connect(DB_URL);
        console.log("✅ Database connected successfully");
    } catch (error) {
        console.error("❌ Database connection failed:", error);
        process.exit(1);
    }
}

async function listTestUsers() {
    try {
        console.log('\n📋 Listing all test users...\n');
        
        const testUsers = await User.find({
            $or: [
                { email: /test.*@example\.com/ },
                { email: /vendor.*@example\.com/ },
                { email: /.*\.test\..*@example\.com/ },
                { firstName: 'Test' },
                { businessName: /Test/ }
            ]
        }).select('email firstName lastName businessName userType createdAt');

        if (testUsers.length === 0) {
            console.log('🎉 No test users found in the database.');
            return [];
        }

        console.log(`Found ${testUsers.length} test user(s):`);
        testUsers.forEach((user, index) => {
            console.log(`${index + 1}. ${user.email} (${user.userType}) - Created: ${user.createdAt.toLocaleDateString()}`);
            if (user.userType === 'user') {
                console.log(`   Name: ${user.firstName} ${user.lastName}`);
            } else if (user.userType === 'vendor') {
                console.log(`   Business: ${user.businessName}`);
            }
        });

        return testUsers;
    } catch (error) {
        console.error('❌ Error listing test users:', error);
        return [];
    }
}

async function cleanupTestUsers() {
    try {
        console.log('\n🧹 Cleaning up test users...\n');
        
        const result = await User.deleteMany({
            $or: [
                { email: /test.*@example\.com/ },
                { email: /vendor.*@example\.com/ },
                { email: /.*\.test\..*@example\.com/ },
                { firstName: 'Test' },
                { businessName: /Test/ }
            ]
        });

        console.log(`✅ Deleted ${result.deletedCount} test user(s)`);
        return result.deletedCount;
    } catch (error) {
        console.error('❌ Error cleaning up test users:', error);
        return 0;
    }
}

async function cleanupSpecificEmails() {
    try {
        console.log('\n🎯 Cleaning up specific test emails...\n');
        
        const specificEmails = [
            '<EMAIL>',
            '<EMAIL>'
        ];

        const result = await User.deleteMany({
            email: { $in: specificEmails }
        });

        console.log(`✅ Deleted ${result.deletedCount} user(s) with specific emails`);
        specificEmails.forEach(email => {
            console.log(`   - ${email}`);
        });

        return result.deletedCount;
    } catch (error) {
        console.error('❌ Error cleaning up specific emails:', error);
        return 0;
    }
}

async function getUserStats() {
    try {
        console.log('\n📊 Database Statistics...\n');
        
        const totalUsers = await User.countDocuments();
        const regularUsers = await User.countDocuments({ userType: 'user' });
        const vendors = await User.countDocuments({ userType: 'vendor' });
        const admins = await User.countDocuments({ userType: 'admin' });
        const verifiedUsers = await User.countDocuments({ 'emailVerification.isVerified': true });

        console.log(`Total Users: ${totalUsers}`);
        console.log(`Regular Users: ${regularUsers}`);
        console.log(`Vendors: ${vendors}`);
        console.log(`Admins: ${admins}`);
        console.log(`Verified Users: ${verifiedUsers}`);
        console.log(`Unverified Users: ${totalUsers - verifiedUsers}`);

    } catch (error) {
        console.error('❌ Error getting user stats:', error);
    }
}

async function main() {
    await connectDB();

    const args = process.argv.slice(2);
    const command = args[0] || 'list';

    switch (command) {
        case 'list':
            await listTestUsers();
            await getUserStats();
            break;
            
        case 'clean':
            await listTestUsers();
            const deleted = await cleanupTestUsers();
            if (deleted > 0) {
                console.log('\n📊 Updated statistics:');
                await getUserStats();
            }
            break;
            
        case 'clean-specific':
            await cleanupSpecificEmails();
            await getUserStats();
            break;
            
        case 'stats':
            await getUserStats();
            break;
            
        default:
            console.log('\n📖 Usage:');
            console.log('  node cleanup-test-users.js [command]');
            console.log('\n🔧 Commands:');
            console.log('  list           - List all test users (default)');
            console.log('  clean          - Delete all test users');
            console.log('  clean-specific - Delete specific test emails (<EMAIL>, <EMAIL>)');
            console.log('  stats          - Show database statistics');
            console.log('\n💡 Examples:');
            console.log('  node cleanup-test-users.js list');
            console.log('  node cleanup-test-users.js clean');
            console.log('  node cleanup-test-users.js clean-specific');
            break;
    }

    await mongoose.connection.close();
    console.log('\n👋 Database connection closed.');
}

main().catch(error => {
    console.error('❌ Script error:', error);
    process.exit(1);
});