const path = require('path');
const fs = require('fs');
const { createUploader, ALLOWED_FILE_TYPES, FILE_SIZE_LIMITS } = require('../../config/upload');

/**
 * Middleware for handling image uploads
 * Supports single image, multiple images, and specific field uploads
 */
const imageUpload = {
  /**
   * Upload a single image
   * @param {string} fieldName - Form field name for the image
   * @returns {Function} Express middleware
   */
  single: (fieldName = 'image') => {
    const uploader = createUploader('image').single(fieldName);
    
    return (req, res, next) => {
      uploader(req, res, (err) => {
        if (err) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: `Image size should not exceed ${FILE_SIZE_LIMITS.image / (1024 * 1024)}MB`
            });
          }
          
          return res.status(400).json({
            success: false,
            error: err.message || 'Error uploading image'
          });
        }
        
        // If no file was uploaded, just continue
        if (!req.file) {
          return next();
        }
        
        // Add file URL to request
        req.fileUrl = `/uploads/images/${path.basename(req.file.path)}`;
        next();
      });
    };
  },
  
  /**
   * Upload multiple images
   * @param {string} fieldName - Form field name for the images
   * @param {number} maxCount - Maximum number of images to upload
   * @returns {Function} Express middleware
   */
  multiple: (fieldName = 'images', maxCount = 5) => {
    const uploader = createUploader('image').array(fieldName, maxCount);
    
    return (req, res, next) => {
      uploader(req, res, (err) => {
        if (err) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: `Each image size should not exceed ${FILE_SIZE_LIMITS.image / (1024 * 1024)}MB`
            });
          }
          
          if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
              success: false,
              error: `Maximum ${maxCount} images allowed`
            });
          }
          
          return res.status(400).json({
            success: false,
            error: err.message || 'Error uploading images'
          });
        }
        
        // If no files were uploaded, just continue
        if (!req.files || req.files.length === 0) {
          return next();
        }
        
        // Add file URLs to request
        req.fileUrls = req.files.map(file => 
          `/uploads/images/${path.basename(file.path)}`
        );
        next();
      });
    };
  },
  
  /**
   * Upload profile avatar
   * @returns {Function} Express middleware
   */
  avatar: () => {
    return imageUpload.single('avatar');
  },
  
  /**
   * Upload product images
   * @param {number} maxCount - Maximum number of product images
   * @returns {Function} Express middleware
   */
  product: (maxCount = 8) => {
    return imageUpload.multiple('productImages', maxCount);
  },
  
  /**
   * Validate image without uploading
   * @param {string} fieldName - Form field name for the image
   * @returns {Function} Express middleware
   */
  validate: (fieldName = 'image') => {
    return (req, res, next) => {
      // If no file in request, continue
      if (!req.files && !req.file) {
        return next();
      }
      
      const file = req.files?.[fieldName]?.[0] || req.file;
      
      // If no specific file, continue
      if (!file) {
        return next();
      }
      
      // Check file type
      if (!ALLOWED_FILE_TYPES.image.includes(file.mimetype)) {
        return res.status(400).json({
          success: false,
          error: `Only ${ALLOWED_FILE_TYPES.image.join(', ')} files are allowed`
        });
      }
      
      // Check file size
      if (file.size > FILE_SIZE_LIMITS.image) {
        return res.status(400).json({
          success: false,
          error: `Image size should not exceed ${FILE_SIZE_LIMITS.image / (1024 * 1024)}MB`
        });
      }
      
      next();
    };
  },
  
  /**
   * Delete an uploaded image
   * @param {string} imagePath - Path to the image
   * @returns {Promise<boolean>} Success status
   */
  deleteImage: async (imagePath) => {
    try {
      // Ensure the path is within the uploads directory
      if (!imagePath.startsWith('/uploads/')) {
        return false;
      }
      
      // Convert URL path to file system path
      const fullPath = path.join(__dirname, '../../../..', imagePath);
      
      // Check if file exists
      if (!fs.existsSync(fullPath)) {
        return false;
      }
      
      // Delete the file
      await fs.promises.unlink(fullPath);
      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      return false;
    }
  }
};

module.exports = imageUpload;