import React from 'react';
import { Carousel } from 'antd';

const AutoScrollCarousel = () => (
  <div className='w-full max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-4xl xl:max-w-6xl h-fit mx-auto'>
    <Carousel autoplay className='w-full h-full'>
      <div>
        <img
          src="https://rukminim2.flixcart.com/fk-p-flap/1620/270/image/9cb1a4418dfd0451.jpg?q=80"
          alt="Carousel 1"
          className="h-44 sm:h-52 md:h-60 lg:h-64 xl:h-72 w-full object-cover rounded"
        />
      </div>
      <div>
        <img
          src="https://rukminim2.flixcart.com/fk-p-flap/1620/270/image/a2a09b43f88343de.jpeg?q=80"
          alt="Carousel 2"
          className="h-44 sm:h-52 md:h-60 lg:h-64 xl:h-72 w-full object-cover rounded"
        />
      </div>
      <div>
        <img
          src="https://rukminim2.flixcart.com/fk-p-flap/1620/270/image/13ad735f73bd8f82.jpeg?q=80"
          alt="Carousel 3"
          className="h-44 sm:h-52 md:h-60 lg:h-64 xl:h-72 w-full object-cover rounded"
        />
      </div>
      <div>
        <img
          src="https://rukminim2.flixcart.com/fk-p-flap/1620/270/image/3b8351d48612fe3d.jpg?q=80"
          alt="Carousel 4"
          className="h-44 sm:h-52 md:h-60 lg:h-64 xl:h-72 w-full object-cover rounded"
        />
      </div>
    </Carousel>
  </div>
);
export default AutoScrollCarousel;