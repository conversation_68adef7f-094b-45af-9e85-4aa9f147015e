const express = require('express');
const router = express.Router();

// Import middleware
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const imageUpload = require('../../middleware/upload/imageUpload');

// Import validation middleware
const {
  validateCreateProduct,
  validateUpdateProduct,
  validateProductList,
  validateProductId,
  validateBulkOperation,
  validateStockUpdate,
  validateProductImages
} = require('../../middleware/validation/productValidation');

// Import controller
const {
  createProduct,
  getProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  updateStock,
  bulkOperation,
  removeImage,
  getProductStats,
  submitForApproval,
  getApprovalHistory
} = require('../../controllers/vendor/productController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType('vendor'));

/**
 * @route   GET /api/vendor/products/stats
 * @desc    Get vendor product statistics
 * @access  Private (Vendor)
 */
router.get('/stats', getProductStats);

/**
 * @route   GET /api/vendor/products
 * @desc    Get vendor's products with pagination and filters
 * @access  Private (Vendor)
 */
router.get('/', validateProductList, getProducts);

/**
 * @route   GET /api/vendor/products/:id
 * @desc    Get single product by ID
 * @access  Private (Vendor)
 */
router.get('/:id', validateProductId, getProduct);

/**
 * @route   POST /api/vendor/products
 * @desc    Create a new product
 * @access  Private (Vendor)
 */
router.post(
  '/',
  imageUpload.product(8), // Allow up to 8 product images
  validateProductImages,
  validateCreateProduct,
  createProduct
);

/**
 * @route   PUT /api/vendor/products/:id
 * @desc    Update product
 * @access  Private (Vendor)
 */
router.put(
  '/:id',
  imageUpload.product(8), // Allow up to 8 additional product images
  validateProductImages,
  validateUpdateProduct,
  updateProduct
);

/**
 * @route   DELETE /api/vendor/products/:id
 * @desc    Delete product
 * @access  Private (Vendor)
 */
router.delete('/:id', validateProductId, deleteProduct);

/**
 * @route   PATCH /api/vendor/products/:id/stock
 * @desc    Update product stock
 * @access  Private (Vendor)
 */
router.patch('/:id/stock', validateStockUpdate, updateStock);

/**
 * @route   POST /api/vendor/products/bulk
 * @desc    Bulk operations on products (activate, deactivate, delete, archive)
 * @access  Private (Vendor)
 */
router.post('/bulk', validateBulkOperation, bulkOperation);

/**
 * @route   DELETE /api/vendor/products/:id/images
 * @desc    Remove product image
 * @access  Private (Vendor)
 */
router.delete('/:id/images', validateProductId, removeImage);

/**
 * @route   PATCH /api/vendor/products/:id/submit-approval
 * @desc    Submit product for approval
 * @access  Private (Vendor)
 */
router.patch('/:id/submit-approval', validateProductId, submitForApproval);

/**
 * @route   GET /api/vendor/products/:id/approval-history
 * @desc    Get product approval history
 * @access  Private (Vendor)
 */
router.get('/:id/approval-history', validateProductId, getApprovalHistory);

module.exports = router;