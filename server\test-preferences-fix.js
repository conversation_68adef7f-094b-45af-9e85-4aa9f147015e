/**
 * Quick test to verify preferences update fix
 * Run with: node test-preferences-fix.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testPreferencesFix() {
    console.log('🔧 Testing Preferences Update Fix...\n');

    try {
        // Create a test user
        const timestamp = Date.now();
        const testUser = {
            firstName: 'Preferences',
            lastName: 'Test',
            email: `preferences.test.${timestamp}@example.com`,
            password: 'TestPassword123',
            userType: 'user'
        };

        console.log('1. Creating test user...');
        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
        const token = registerResponse.data.data.token;
        console.log('   ✅ Test user created\n');

        // Test various preference updates
        const preferenceTests = [
            {
                name: 'Valid preferences update',
                data: {
                    preferences: {
                        language: 'en',
                        currency: 'USD',
                        timezone: 'America/New_York'
                    }
                },
                shouldSucceed: true
            },
            {
                name: 'Invalid language preference',
                data: {
                    preferences: {
                        language: 'invalid_lang'
                    }
                },
                shouldSucceed: true // Should succeed but ignore invalid language
            },
            {
                name: 'Invalid currency preference',
                data: {
                    preferences: {
                        currency: 'INVALID'
                    }
                },
                shouldSucceed: true // Should succeed but ignore invalid currency
            },
            {
                name: 'Mixed valid and invalid preferences',
                data: {
                    firstName: 'Updated',
                    preferences: {
                        language: 'es',
                        currency: 'INVALID',
                        timezone: 'Europe/London'
                    }
                },
                shouldSucceed: true
            },
            {
                name: 'Preferences with notifications',
                data: {
                    preferences: {
                        language: 'fr',
                        notifications: {
                            email: {
                                marketing: false,
                                orderUpdates: true
                            }
                        }
                    }
                },
                shouldSucceed: true
            }
        ];

        for (let i = 0; i < preferenceTests.length; i++) {
            const test = preferenceTests[i];
            console.log(`${i + 2}. ${test.name}:`);

            try {
                const response = await axios.put(`${BASE_URL}/auth/profile`, test.data, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (test.shouldSucceed) {
                    console.log(`   ✅ Success: ${response.data.message}`);
                    if (response.data.data.updatedFields) {
                        console.log(`   📝 Updated: ${response.data.data.updatedFields.join(', ')}`);
                    }
                } else {
                    console.log(`   ❌ Should have failed but succeeded`);
                }

            } catch (error) {
                if (error.response) {
                    if (test.shouldSucceed) {
                        console.log(`   ❌ Unexpected error: ${error.response.status} - ${error.response.data.message}`);
                        if (error.response.data.errors) {
                            console.log(`   📝 Errors:`);
                            error.response.data.errors.forEach(err => {
                                console.log(`      - ${err.field}: ${err.message}`);
                            });
                        }
                    } else {
                        console.log(`   ✅ Expected error: ${error.response.status} - ${error.response.data.message}`);
                    }
                } else {
                    console.log(`   ❌ Network error: ${error.message}`);
                }
            }

            console.log(''); // Empty line
        }

        // Get final profile to see what was actually updated
        console.log(`${preferenceTests.length + 2}. Getting final profile state...`);
        const finalProfile = await axios.get(`${BASE_URL}/auth/profile`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        const user = finalProfile.data.data.user;
        console.log('   📋 Final profile state:');
        console.log(`   📧 Email: ${user.email}`);
        console.log(`   👤 Name: ${user.firstName} ${user.lastName}`);
        console.log(`   🌐 Language: ${user.preferences?.language || 'not set'}`);
        console.log(`   💰 Currency: ${user.preferences?.currency || 'not set'}`);
        console.log(`   🕐 Timezone: ${user.preferences?.timezone || 'not set'}`);

        console.log('\n🎉 Preferences test completed!');
        console.log('\n💡 Key improvements:');
        console.log('   - Preferences updates no longer cause 500 errors');
        console.log('   - Invalid preference values are ignored (not causing failures)');
        console.log('   - Valid preferences are properly saved');
        console.log('   - Mixed updates (profile + preferences) work correctly');

    } catch (error) {
        console.error('❌ Test error:', error.response?.data || error.message);
    }
}

// Main execution
testPreferencesFix();