import React, { useEffect, useState } from 'react';
import { Pagination } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { ProductInfo } from '../../utils/Api';

const ProductCard = ({ initialPage = 1 }) => {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(initialPage);
    const [pageSize] = useState(20);
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        const fetchProducts = async () => {
            try {
                const data = await ProductInfo();
                setProducts(data);
            } catch (err) {
                console.error(err);
                setError('Failed to load products');
            } finally {
                setLoading(false);
            }
        };
        
        fetchProducts();
    }, []);

    // Update current page when initialPage prop changes
    useEffect(() => {
        setCurrentPage(initialPage);
    }, [initialPage]);

    if (loading) {
        // Simple skeleton placeholders
        return (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 p-4">
                {Array.from({ length: 10 }).map((_, i) => (
                    <div key={i} className="animate-pulse bg-gray-200 h-80 rounded-lg" />
                ))}
            </div>
        );
    }

    if (error) {
        return <div className="text-red-500 p-4">{error}</div>;
    }

    if (!products.length) {
        return <div className="p-4">No products found.</div>;
    }

    // Calculate pagination
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const displayedProducts = products.slice(startIndex, endIndex);

    const handlePageChange = (page) => {
        setCurrentPage(page);
        
        // If we're on the home page and user clicks page 2 or higher, navigate to /page route
        if (location.pathname === '/' && page > 1) {
            navigate(`/page?q=${page}`);
        } 
        // If we're on /page route, update the URL
        else if (location.pathname === '/page') {
            navigate(`/page?q=${page}`);
        }
        
        // Scroll to top when page changes
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleProductClick = (productId) => {
        navigate(`/product/${productId}`);
    };

    const handleWishlistClick = (e, productId) => {
        e.stopPropagation();
        console.log('Added to wishlist:', productId);
        // Add wishlist functionality here
    };

    const handleCartClick = (e, productId) => {
        e.stopPropagation();
        console.log('Added to cart:', productId);
        // Add cart functionality here
    };

    return (
        <div>
            <div className="grid gap-4 p-4" 
                 style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))' }}>
                {displayedProducts.map(product => (
                    <div
                        key={product.id}
                        onClick={() => handleProductClick(product.id)}
                        className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-md hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                    >
                        {/* Image container: square */}
                        <div className="relative w-full pb-[100%] bg-gray-100">
                            <img
                                src={product.images?.[0] || '/placeholder.png'}
                                alt={product.title}
                                className="absolute inset-0 w-full h-full object-cover"
                                onError={(e) => {
                                    e.target.src = 'https://via.placeholder.com/200x200?text=No+Image';
                                }}
                            />
                        </div>

                        {/* Content */}
                        <div className="p-3 flex flex-col justify-between h-44">
                            <div>
                                {/* Title: clamp to 2 lines */}
                                <h3 className="text-sm font-medium text-gray-800 leading-tight mb-1" 
                                    style={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        minHeight: '2.5em'
                                    }}>
                                    {product.title}
                                </h3>
                                
                                {/* Price */}
                                <p className="text-base font-bold text-[#ed2b2a] mb-1">
                                    ${product.price}
                                </p>
                                
                                {/* MOQ & Shipping */}
                                <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                                    <span>MOQ: 1 piece</span> 
                                    <span className="text-green-600">Free Shipping</span>
                                </div>
                            </div>

                            {/* Footer row: location + actions */}
                            <div className="flex items-center justify-between mt-auto pt-2 border-t border-gray-50">
                                <span className="text-xs text-gray-500 flex items-center">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3 w-3 mr-1"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M12 2C8.14 2 5 5.14 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.86-3.14-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5S10.62 6.5 12 6.5s2.5 1.12 2.5 2.5S13.38 11.5 12 11.5z"/>
                                    </svg>
                                    CN
                                </span>
                                <div className="flex space-x-2">
                                    <button 
                                        onClick={(e) => handleWishlistClick(e, product.id)}
                                        className="text-gray-400 hover:text-red-500 cursor-pointer transition-colors duration-200 text-base p-1 rounded hover:bg-gray-50"
                                        title="Add to Wishlist"
                                    >
                                        ♡
                                    </button>
                                    <button 
                                        onClick={(e) => handleCartClick(e, product.id)}
                                        className="text-gray-400 hover:text-blue-600 cursor-pointer transition-colors duration-200 text-sm p-1 rounded hover:bg-gray-50"
                                        title="Add to Cart"
                                    >
                                        🛒
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
            
            {/* Pagination */}
            {products.length > pageSize && (
                <div className="flex justify-center p-4">
                    <Pagination
                        current={currentPage}
                        total={products.length}
                        pageSize={pageSize}
                        onChange={handlePageChange}
                        showSizeChanger={false}
                        showQuickJumper={true}
                        showTotal={(total, range) => 
                            `${range[0]}-${range[1]} of ${total} products`
                        }
                    />
                </div>
            )}
        </div>
    );
};

export default ProductCard;