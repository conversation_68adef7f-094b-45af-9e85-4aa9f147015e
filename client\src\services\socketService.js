import { io } from 'socket.io-client';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = new Map();
  }

  connect(token) {
    if (this.socket) {
      this.disconnect();
    }

    this.socket = io(import.meta.env.VITE_API_URL?.replace('/api', '') || 'https://multi-vendor-server-1tb9.onrender.com', {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventListeners();
    return this.socket;
  }

  setupEventListeners() {
    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.isConnected = true;
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.isConnected = false;
    });

    // Handle ping/pong for connection health
    this.socket.on('pong', () => {
      // Connection is healthy
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Admin-specific methods
  subscribeToAdminUpdates(callback) {
    if (!this.socket) return;

    this.socket.on('admin:dashboard-update', callback);
    this.socket.on('admin:new-order', callback);
    this.socket.on('admin:new-user', callback);
    this.socket.on('admin:new-vendor', callback);
    this.socket.on('admin:low-stock-alert', callback);
    this.socket.on('admin:payment-update', callback);
    this.socket.on('admin:order-status-update', callback);

    // Request initial dashboard data
    this.socket.emit('admin:request-dashboard');
  }

  unsubscribeFromAdminUpdates() {
    if (!this.socket) return;

    this.socket.off('admin:dashboard-update');
    this.socket.off('admin:new-order');
    this.socket.off('admin:new-user');
    this.socket.off('admin:new-vendor');
    this.socket.off('admin:low-stock-alert');
    this.socket.off('admin:payment-update');
    this.socket.off('admin:order-status-update');
  }

  subscribeToDataType(dataTypes) {
    if (!this.socket) return;
    this.socket.emit('admin:subscribe', dataTypes);
  }

  unsubscribeFromDataType(dataTypes) {
    if (!this.socket) return;
    this.socket.emit('admin:unsubscribe', dataTypes);
  }

  // Vendor-specific methods
  subscribeToVendorUpdates(callback) {
    if (!this.socket) return;

    this.socket.on('vendor:new-order', callback);
    this.socket.on('vendor:order-update', callback);
    this.socket.on('vendor:low-stock-alert', callback);

    this.socket.emit('vendor:subscribe-orders');
    this.socket.emit('vendor:subscribe-products');
  }

  unsubscribeFromVendorUpdates() {
    if (!this.socket) return;

    this.socket.off('vendor:new-order');
    this.socket.off('vendor:order-update');
    this.socket.off('vendor:low-stock-alert');
  }

  // Customer-specific methods
  subscribeToCustomerUpdates(callback) {
    if (!this.socket) return;

    this.socket.on('customer:order-update', callback);
    this.socket.on('customer:payment-update', callback);

    this.socket.emit('customer:subscribe-orders');
  }

  unsubscribeFromCustomerUpdates() {
    if (!this.socket) return;

    this.socket.off('customer:order-update');
    this.socket.off('customer:payment-update');
  }

  // Generic event methods
  on(event, callback) {
    if (!this.socket) return;
    this.socket.on(event, callback);
  }

  off(event, callback) {
    if (!this.socket) return;
    this.socket.off(event, callback);
  }

  emit(event, data) {
    if (!this.socket) return;
    this.socket.emit(event, data);
  }

  // Health check
  ping() {
    if (!this.socket) return;
    this.socket.emit('ping');
  }

  getConnectionStatus() {
    return this.isConnected;
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;