const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Product description is required'],
    trim: true,
    maxlength: [2000, 'Product description cannot exceed 2000 characters']
  },
  shortDescription: {
    type: String,
    trim: true,
    maxlength: [500, 'Short description cannot exceed 500 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  subcategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  },
  brand: {
    type: String,
    trim: true,
    maxlength: [100, 'Brand name cannot exceed 100 characters']
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  barcode: {
    type: String,
    trim: true,
    sparse: true
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      trim: true
    },
    isPrimary: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    }
  }],
  pricing: {
    basePrice: {
      type: Number,
      required: [true, 'Base price is required'],
      min: [0, 'Price cannot be negative']
    },
    salePrice: {
      type: Number,
      min: [0, 'Sale price cannot be negative']
    },
    costPrice: {
      type: Number,
      min: [0, 'Cost price cannot be negative']
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true
    },
    taxClass: {
      type: String,
      enum: ['standard', 'reduced', 'zero', 'exempt'],
      default: 'standard'
    },
    taxRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    }
  },
  inventory: {
    trackQuantity: {
      type: Boolean,
      default: true
    },
    quantity: {
      type: Number,
      required: function() { return this.inventory.trackQuantity; },
      min: [0, 'Quantity cannot be negative'],
      default: 0
    },
    lowStockThreshold: {
      type: Number,
      min: 0,
      default: 5
    },
    allowBackorders: {
      type: Boolean,
      default: false
    },
    stockStatus: {
      type: String,
      enum: ['in_stock', 'out_of_stock', 'on_backorder'],
      default: 'in_stock'
    }
  },
  variants: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    options: [{
      name: {
        type: String,
        required: true,
        trim: true
      },
      value: {
        type: String,
        required: true,
        trim: true
      },
      priceModifier: {
        type: Number,
        default: 0
      },
      sku: {
        type: String,
        trim: true
      },
      quantity: {
        type: Number,
        min: 0,
        default: 0
      },
      image: String
    }]
  }],
  attributes: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    value: {
      type: String,
      required: true,
      trim: true
    },
    unit: {
      type: String,
      trim: true
    }
  }],
  specifications: {
    weight: {
      value: Number,
      unit: {
        type: String,
        enum: ['kg', 'g', 'lb', 'oz'],
        default: 'kg'
      }
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      unit: {
        type: String,
        enum: ['cm', 'm', 'in', 'ft'],
        default: 'cm'
      }
    },
    material: String,
    color: String,
    size: String
  },
  shipping: {
    weight: {
      type: Number,
      min: 0
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    },
    shippingClass: {
      type: String,
      enum: ['standard', 'heavy', 'fragile', 'digital'],
      default: 'standard'
    },
    freeShipping: {
      type: Boolean,
      default: false
    },
    shippingCost: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  seo: {
    metaTitle: {
      type: String,
      trim: true,
      maxlength: [60, 'Meta title cannot exceed 60 characters']
    },
    metaDescription: {
      type: String,
      trim: true,
      maxlength: [160, 'Meta description cannot exceed 160 characters']
    },
    keywords: [{
      type: String,
      trim: true
    }],
    canonicalUrl: String
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'inactive', 'archived'],
    default: 'draft'
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'password_protected'],
    default: 'public'
  },
  featured: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  reviews: {
    averageRating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    totalReviews: {
      type: Number,
      default: 0
    },
    ratingDistribution: {
      1: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      5: { type: Number, default: 0 }
    }
  },
  sales: {
    totalSold: {
      type: Number,
      default: 0
    },
    totalRevenue: {
      type: Number,
      default: 0
    },
    lastSaleDate: Date
  },
  publishedAt: Date,
  lastModified: {
    type: Date,
    default: Date.now
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (slug and sku indexes are automatically created by unique: true)
productSchema.index({ vendor: 1 });
productSchema.index({ category: 1 });
productSchema.index({ name: 'text', description: 'text', tags: 'text' });
productSchema.index({ status: 1 });
productSchema.index({ featured: 1 });
productSchema.index({ 'pricing.basePrice': 1 });
productSchema.index({ 'reviews.averageRating': -1 });
productSchema.index({ 'sales.totalSold': -1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ publishedAt: -1 });

// Virtual for current price (sale price if available, otherwise base price)
productSchema.virtual('currentPrice').get(function() {
  return this.pricing.salePrice || this.pricing.basePrice;
});

// Virtual for discount percentage
productSchema.virtual('discountPercentage').get(function() {
  if (this.pricing.salePrice && this.pricing.salePrice < this.pricing.basePrice) {
    return Math.round(((this.pricing.basePrice - this.pricing.salePrice) / this.pricing.basePrice) * 100);
  }
  return 0;
});

// Virtual for stock status
productSchema.virtual('isInStock').get(function() {
  if (!this.inventory.trackQuantity) return true;
  return this.inventory.quantity > 0 || this.inventory.allowBackorders;
});

// Virtual for low stock status
productSchema.virtual('isLowStock').get(function() {
  if (!this.inventory.trackQuantity) return false;
  return this.inventory.quantity <= this.inventory.lowStockThreshold && this.inventory.quantity > 0;
});

// Virtual for primary image
productSchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary || this.images[0] || null;
});

// Pre-save middleware to generate slug
productSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  
  // Update lastModified
  this.lastModified = new Date();
  
  // Ensure only one primary image
  if (this.images && this.images.length > 0) {
    const primaryImages = this.images.filter(img => img.isPrimary);
    if (primaryImages.length > 1) {
      this.images.forEach((img, index) => {
        if (index > 0 && img.isPrimary) {
          img.isPrimary = false;
        }
      });
    } else if (primaryImages.length === 0) {
      this.images[0].isPrimary = true;
    }
  }
  
  // Update stock status based on quantity
  if (this.inventory.trackQuantity) {
    if (this.inventory.quantity === 0) {
      this.inventory.stockStatus = this.inventory.allowBackorders ? 'on_backorder' : 'out_of_stock';
    } else {
      this.inventory.stockStatus = 'in_stock';
    }
  }
  
  next();
});

// Static method to get product statistics
productSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        totalProducts: { $sum: 1 },
        activeProducts: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        draftProducts: {
          $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
        },
        inactiveProducts: {
          $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
        },
        featuredProducts: {
          $sum: { $cond: ['$featured', 1, 0] }
        },
        outOfStockProducts: {
          $sum: { $cond: [{ $eq: ['$inventory.stockStatus', 'out_of_stock'] }, 1, 0] }
        },
        lowStockProducts: {
          $sum: { $cond: [{ $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }, 1, 0] }
        },
        totalRevenue: { $sum: '$sales.totalRevenue' },
        totalSold: { $sum: '$sales.totalSold' },
        averagePrice: { $avg: '$pricing.basePrice' },
        averageRating: { $avg: '$reviews.averageRating' }
      }
    }
  ]);
  
  return stats[0] || {
    totalProducts: 0,
    activeProducts: 0,
    draftProducts: 0,
    inactiveProducts: 0,
    featuredProducts: 0,
    outOfStockProducts: 0,
    lowStockProducts: 0,
    totalRevenue: 0,
    totalSold: 0,
    averagePrice: 0,
    averageRating: 0
  };
};

// Static method to get top selling products
productSchema.statics.getTopSelling = function(limit = 10) {
  return this.find({ status: 'active' })
    .populate('vendor', 'businessName')
    .populate('category', 'name')
    .sort({ 'sales.totalSold': -1 })
    .limit(limit)
    .select('name pricing.basePrice sales vendor category images');
};

// Static method to get recent products
productSchema.statics.getRecent = function(limit = 10) {
  return this.find({ status: 'active' })
    .populate('vendor', 'businessName')
    .populate('category', 'name')
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('name pricing.basePrice vendor category images createdAt');
};

// Static method to get low stock products
productSchema.statics.getLowStock = function() {
  return this.find({
    'inventory.trackQuantity': true,
    $expr: { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }
  })
    .populate('vendor', 'businessName')
    .select('name sku inventory vendor')
    .sort({ 'inventory.quantity': 1 });
};

// Instance method to update stock
productSchema.methods.updateStock = function(quantity, operation = 'set') {
  if (!this.inventory.trackQuantity) return this;
  
  switch (operation) {
    case 'add':
      this.inventory.quantity += quantity;
      break;
    case 'subtract':
      this.inventory.quantity = Math.max(0, this.inventory.quantity - quantity);
      break;
    case 'set':
    default:
      this.inventory.quantity = Math.max(0, quantity);
      break;
  }
  
  return this.save();
};

// Instance method to update sales data
productSchema.methods.recordSale = function(quantity, price) {
  this.sales.totalSold += quantity;
  this.sales.totalRevenue += (price * quantity);
  this.sales.lastSaleDate = new Date();
  
  // Update stock if tracking
  if (this.inventory.trackQuantity) {
    this.inventory.quantity = Math.max(0, this.inventory.quantity - quantity);
  }
  
  return this.save();
};

// Instance method to update review data
productSchema.methods.updateReviews = function(rating) {
  const currentTotal = this.reviews.averageRating * this.reviews.totalReviews;
  this.reviews.totalReviews += 1;
  this.reviews.averageRating = (currentTotal + rating) / this.reviews.totalReviews;
  this.reviews.ratingDistribution[rating] += 1;
  
  return this.save();
};

module.exports = mongoose.model('Product', productSchema);