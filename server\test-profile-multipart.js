/**
 * Test profile update with multipart form data
 */

require('dotenv').config();
const axios = require('axios');
const FormData = require('form-data');

const BASE_URL = 'http://localhost:5000/api';

async function loginUser() {
    console.log('🔐 Logging in to get authentication token...\n');
    
    try {
        const response = await axios.post(`${BASE_URL}/auth/login`, {
            email: '<EMAIL>', // Use the email from our previous test
            password: 'Free@009'
        });
        
        console.log('✅ Login successful');
        console.log(`Token: ${response.data.data.token.substring(0, 20)}...`);
        return response.data.data.token;
        
    } catch (error) {
        console.log('❌ Login failed');
        if (error.response) {
            console.log(`Status: ${error.response.status}`);
            console.log(`Message: ${error.response.data.message}`);
        }
        throw error;
    }
}

async function testProfileUpdateWithFormData(token) {
    console.log('\n📝 Testing profile update with multipart form data...\n');
    
    // Create form data
    const formData = new FormData();
    formData.append('address', '123 Main Street');
    formData.append('city', 'New York');
    formData.append('state', 'NY');
    formData.append('zipCode', '10001');
    formData.append('country', 'United States');
    
    console.log('📋 Form Data Fields:');
    console.log('- address: 123 Main Street');
    console.log('- city: New York');
    console.log('- state: NY');
    console.log('- zipCode: 10001');
    console.log('- country: United States');
    
    try {
        const response = await axios.put(`${BASE_URL}/auth/profile`, formData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                ...formData.getHeaders()
            },
            timeout: 10000
        });
        
        console.log('\n✅ PROFILE UPDATE SUCCESSFUL!');
        console.log(`Status: ${response.status}`);
        console.log(`Message: ${response.data.message}`);
        console.log(`Updated Fields: ${response.data.data.updatedFields.join(', ')}`);
        
        // Display updated user data
        const user = response.data.data.user;
        console.log('\n📋 Updated Profile:');
        console.log(`- Name: ${user.firstName} ${user.lastName}`);
        console.log(`- Email: ${user.email}`);
        console.log(`- Address: ${user.address || 'Not set'}`);
        console.log(`- City: ${user.city || 'Not set'}`);
        console.log(`- State: ${user.state || 'Not set'}`);
        console.log(`- ZIP Code: ${user.zipCode || 'Not set'}`);
        console.log(`- Country: ${user.country || 'Not set'}`);
        
    } catch (error) {
        console.log('\n❌ PROFILE UPDATE FAILED');
        if (error.response) {
            console.log(`Status: ${error.response.status}`);
            console.log(`Message: ${error.response.data.message}`);
            
            if (error.response.data.errors) {
                console.log('\n📝 Validation Errors:');
                error.response.data.errors.forEach(err => {
                    console.log(`- ${err.field}: ${err.message}`);
                });
            }
        } else if (error.request) {
            console.log('❌ NETWORK ERROR');
            console.log('No response received from server');
        } else {
            console.log('❌ REQUEST ERROR');
            console.log('Error:', error.message);
        }
        throw error;
    }
}

async function testProfileUpdateWithJSON(token) {
    console.log('\n📝 Testing profile update with JSON data (for comparison)...\n');
    
    const updateData = {
        address: '456 Oak Avenue',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'United States'
    };
    
    console.log('📋 JSON Data:');
    console.log(JSON.stringify(updateData, null, 2));
    
    try {
        const response = await axios.put(`${BASE_URL}/auth/profile`, updateData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('\n✅ JSON PROFILE UPDATE SUCCESSFUL!');
        console.log(`Status: ${response.status}`);
        console.log(`Message: ${response.data.message}`);
        console.log(`Updated Fields: ${response.data.data.updatedFields.join(', ')}`);
        
    } catch (error) {
        console.log('\n❌ JSON PROFILE UPDATE FAILED');
        if (error.response) {
            console.log(`Status: ${error.response.status}`);
            console.log(`Message: ${error.response.data.message}`);
        }
        throw error;
    }
}

async function main() {
    try {
        console.log('🧪 Testing Profile Update with Multipart Form Data\n');
        console.log('=' .repeat(50));
        
        // Step 1: Login to get token
        const token = await loginUser();
        
        // Step 2: Test multipart form data
        await testProfileUpdateWithFormData(token);
        
        // Step 3: Test JSON data for comparison
        await testProfileUpdateWithJSON(token);
        
        console.log('\n🎉 All tests completed successfully!');
        console.log('\n💡 The profile update endpoint now supports both:');
        console.log('   1. multipart/form-data (for forms with file uploads)');
        console.log('   2. application/json (for simple data updates)');
        
    } catch (error) {
        console.log('\n❌ Test failed:', error.message);
        process.exit(1);
    }
}

main().catch(console.error);