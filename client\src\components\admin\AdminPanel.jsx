import React, { useState } from 'react';
import AdminLayout from './AdminLayout';
import ResponsiveAdminDashboard from './ResponsiveAdminDashboard';
import UsersManagement from './sections/UsersManagement';
import VendorsManagement from './sections/VendorsManagement';
import ProductsManagement from './sections/ProductsManagement';
import OrdersManagement from './sections/OrdersManagement';
import Analytics from './sections/Analytics';
import Settings from './sections/Settings';

const AdminPanel = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <ResponsiveAdminDashboard />;
      case 'users':
        return <UsersManagement />;
      case 'vendors':
        return <VendorsManagement />;
      case 'products':
        return <ProductsManagement />;
      case 'orders':
        return <OrdersManagement />;
      case 'analytics':
        return <ResponsiveAdminDashboard />;
      case 'settings':
        return <Settings />;
      default:
        return <ResponsiveAdminDashboard />;
    }
  };

  return (
    <AdminLayout 
      activeKey={activeSection} 
      onMenuSelect={setActiveSection}
    >
      {renderContent()}
    </AdminLayout>
  );
};

export default AdminPanel;