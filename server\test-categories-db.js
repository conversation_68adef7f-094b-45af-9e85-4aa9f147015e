const mongoose = require('mongoose');
const Category = require('./src/models/Category');
require('dotenv').config();

async function testCategoriesInDB() {
  try {
    console.log('🔍 Checking categories in database...');
    
    // Connect to database
    await mongoose.connect(process.env.DATABASE_URL || 'mongodb://localhost:27017/ecommerce');
    console.log('✅ Connected to database');

    // Get categories count
    const totalCategories = await Category.countDocuments();
    console.log(`📊 Total categories in DB: ${totalCategories}`);

    // Get active categories
    const activeCategories = await Category.find({ status: 'active' })
      .select('name _id parent level')
      .sort({ sortOrder: 1, name: 1 })
      .lean();

    console.log(`✅ Active categories: ${activeCategories.length}`);
    
    console.log('\n📋 Categories List:');
    activeCategories.forEach((category, index) => {
      const indent = category.parent ? '  └─�� ' : '├── ';
      console.log(`${indent}${category.name} (ID: ${category._id})`);
    });

    // Test the API response format
    const apiResponse = {
      success: true,
      data: {
        categories: activeCategories
      }
    };

    console.log('\n🧪 Sample API Response:');
    console.log(JSON.stringify(apiResponse, null, 2));

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

testCategoriesInDB();