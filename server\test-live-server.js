/**
 * Test script for live server
 * Run with: node test-live-server.js
 */

const axios = require('axios');

// Use localhost for local testing
const BASE_URL = 'http://localhost:5000/api';

async function testLiveServer() {
    console.log('🌐 Testing Live Server Profile Updates...\n');
    console.log(`Server URL: ${BASE_URL}\n`);

    try {
        // Step 1: Check if server is accessible
        console.log('1. Checking server health...');
        try {
            const healthResponse = await axios.get(`${BASE_URL}/auth/health`);
            console.log(`   ✅ Server is healthy: ${healthResponse.data.message}`);
        } catch (error) {
            console.log(`   ❌ Server health check failed: ${error.message}`);
            return;
        }

        // Step 2: Try to create a test user
        console.log('\n2. Creating test user...');
        const timestamp = Date.now();
        const testUser = {
            firstName: 'Live',
            lastName: 'Test',
            email: `live.test.${timestamp}@example.com`,
            password: 'TestPassword123',
            userType: 'user'
        };

        let token = null;
        try {
            const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
            token = registerResponse.data.data.token;
            console.log(`   ✅ Test user created: ${testUser.email}`);
        } catch (error) {
            console.log(`   ❌ User creation failed: ${error.response?.data?.message || error.message}`);
            return;
        }

        // Step 3: Test profile updates that might be causing 400 errors
        console.log('\n3. Testing profile update scenarios...\n');

        const testScenarios = [
            {
                name: 'Empty object (likely causing 400)',
                data: {},
                shouldFail: true
            },
            {
                name: 'Valid address update',
                data: { address: '123 Main Street' },
                shouldFail: false
            },
            {
                name: 'Valid city update',
                data: { city: 'New York' },
                shouldFail: false
            },
            {
                name: 'Multiple fields',
                data: { 
                    address: '456 Oak Ave',
                    city: 'Los Angeles',
                    state: 'CA'
                },
                shouldFail: false
            },
            {
                name: 'Invalid field only',
                data: { invalidField: 'test' },
                shouldFail: true
            }
        ];

        for (let i = 0; i < testScenarios.length; i++) {
            const scenario = testScenarios[i];
            console.log(`   ${i + 1}. ${scenario.name}:`);
            console.log(`      Data: ${JSON.stringify(scenario.data)}`);

            try {
                const response = await axios.put(`${BASE_URL}/auth/profile`, scenario.data, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000 // 10 second timeout
                });

                if (scenario.shouldFail) {
                    console.log(`      ⚠️ Expected failure but got success: ${response.data.message}`);
                } else {
                    console.log(`      ✅ Success: ${response.data.message}`);
                    if (response.data.data?.updatedFields) {
                        console.log(`      📝 Updated: ${response.data.data.updatedFields.join(', ')}`);
                    }
                }

            } catch (error) {
                if (error.response) {
                    const status = error.response.status;
                    const message = error.response.data.message;
                    
                    if (scenario.shouldFail) {
                        console.log(`      ✅ Expected error: ${status} - ${message}`);
                        
                        // Check if this is the same error the user is experiencing
                        if (status === 400) {
                            console.log(`      🎯 This matches the user's 400 error!`);
                            if (error.response.data.allowedFields) {
                                console.log(`      📋 Allowed fields: ${error.response.data.allowedFields.join(', ')}`);
                            }
                            if (error.response.data.receivedFields) {
                                console.log(`      📥 Received fields: ${error.response.data.receivedFields.join(', ')}`);
                            }
                        }
                    } else {
                        console.log(`      ❌ Unexpected error: ${status} - ${message}`);
                    }
                } else if (error.code === 'ECONNABORTED') {
                    console.log(`      ❌ Request timeout - server might be slow`);
                } else {
                    console.log(`      ❌ Network error: ${error.message}`);
                }
            }

            console.log('');
        }

        // Step 4: Test with the specific user's email pattern
        console.log('4. Testing with similar email pattern to problematic user...');
        
        try {
            // Try to register a user with similar email pattern
            const similarUser = {
                firstName: 'Similar',
                lastName: 'User',
                email: `test.${timestamp}@forexru.com`, // <NAME_EMAIL>
                password: 'TestPassword123',
                userType: 'user'
            };

            const similarRegResponse = await axios.post(`${BASE_URL}/auth/register`, similarUser);
            const similarToken = similarRegResponse.data.data.token;
            
            console.log(`   ✅ Created similar user: ${similarUser.email}`);
            
            // Test address update with this user
            const addressResponse = await axios.put(`${BASE_URL}/auth/profile`, {
                address: '789 Test Street',
                city: 'Test City'
            }, {
                headers: {
                    'Authorization': `Bearer ${similarToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log(`   ✅ Address update successful: ${addressResponse.data.message}`);
            
        } catch (error) {
            console.log(`   ❌ Similar user test failed: ${error.response?.data?.message || error.message}`);
        }

        console.log('\n🎉 Live server tests completed!');

    } catch (error) {
        console.error('❌ Test error:', error.response?.data || error.message);
    }
}

// Test common client-side issues that might cause 400 errors
async function testClientIssues() {
    console.log('\n\n🔧 Testing Common Client-Side Issues...\n');

    try {
        // Create a test user for client issue testing
        const timestamp = Date.now();
        const testUser = {
            firstName: 'Client',
            lastName: 'Issue',
            email: `client.issue.${timestamp}@example.com`,
            password: 'TestPassword123',
            userType: 'user'
        };

        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
        const token = registerResponse.data.data.token;

        console.log('Testing potential client-side issues...\n');

        // Common issues that cause 400 errors
        const clientIssues = [
            {
                name: 'Missing Authorization header',
                test: async () => {
                    return axios.put(`${BASE_URL}/auth/profile`, { address: '123 Test St' }, {
                        headers: {
                            'Content-Type': 'application/json'
                            // Missing Authorization header
                        }
                    });
                }
            },
            {
                name: 'Malformed Authorization header',
                test: async () => {
                    return axios.put(`${BASE_URL}/auth/profile`, { address: '123 Test St' }, {
                        headers: {
                            'Authorization': 'InvalidFormat token123',
                            'Content-Type': 'application/json'
                        }
                    });
                }
            },
            {
                name: 'Missing Content-Type header',
                test: async () => {
                    return axios.put(`${BASE_URL}/auth/profile`, { address: '123 Test St' }, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                            // Missing Content-Type
                        }
                    });
                }
            }
        ];

        for (let i = 0; i < clientIssues.length; i++) {
            const issue = clientIssues[i];
            console.log(`${i + 1}. ${issue.name}:`);

            try {
                const response = await issue.test();
                console.log(`   ⚠️ Unexpected success: ${response.status}`);
            } catch (error) {
                if (error.response) {
                    console.log(`   ✅ Expected error: ${error.response.status} - ${error.response.data.message}`);
                } else {
                    console.log(`   ❌ Network error: ${error.message}`);
                }
            }

            console.log('');
        }

    } catch (error) {
        console.error('❌ Client issue test error:', error.response?.data || error.message);
    }
}

// Main execution
async function main() {
    await testLiveServer();
    await testClientIssues();
    
    console.log('\n📋 Summary for Live Server:');
    console.log('   ✅ Profile updates work correctly with valid data');
    console.log('   ❌ 400 errors occur with empty/invalid request bodies');
    console.log('   🎯 <NAME_EMAIL> is likely sending empty data');
    console.log('\n💡 Solutions:');
    console.log('   1. Check client-side code sending the profile update request');
    console.log('   2. Ensure request body contains valid field data');
    console.log('   3. Verify Content-Type: application/json header is set');
    console.log('   4. Check browser dev tools for actual request payload');
    console.log('   5. Add client-side validation before sending requests');
}

main();