/**
 * Test script to verify local development setup
 * Run with: node test-local-setup.js
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:5000';
const FRONTEND_URL = 'http://localhost:5173';

async function testLocalSetup() {
    console.log('🧪 Testing Local Development Setup...\n');

    // Test 1: Backend Health Check
    console.log('1. Testing Backend Server...');
    try {
        const response = await axios.get(`${BACKEND_URL}/api/health`);
        console.log(`   ✅ Backend is running: ${response.data.message}`);
        console.log(`   📊 Environment: ${response.data.environment}`);
    } catch (error) {
        console.log(`   ❌ Backend not accessible: ${error.message}`);
        console.log(`   💡 Make sure to run: cd server && npm run dev`);
        return;
    }

    // Test 2: CORS Configuration
    console.log('\n2. Testing CORS Configuration...');
    try {
        const response = await axios.get(`${BACKEND_URL}/api/auth/health`, {
            headers: {
                'Origin': 'http://localhost:5173'
            }
        });
        console.log(`   ✅ CORS is properly configured for localhost:5173`);
    } catch (error) {
        console.log(`   ❌ CORS issue: ${error.message}`);
    }

    // Test 3: Frontend Accessibility (basic check)
    console.log('\n3. Testing Frontend Accessibility...');
    try {
        const response = await axios.get(FRONTEND_URL, { timeout: 5000 });
        console.log(`   ✅ Frontend is accessible at ${FRONTEND_URL}`);
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log(`   ❌ Frontend not running at ${FRONTEND_URL}`);
            console.log(`   💡 Make sure to run: cd client && npm run dev`);
        } else {
            console.log(`   ⚠️ Frontend check inconclusive: ${error.message}`);
        }
    }

    // Test 4: API Endpoints
    console.log('\n4. Testing Key API Endpoints...');
    
    const endpoints = [
        { path: '/api/health', name: 'Health Check' },
        { path: '/api/auth/health', name: 'Auth Health' },
        { path: '/', name: 'Root Endpoint' }
    ];

    for (const endpoint of endpoints) {
        try {
            const response = await axios.get(`${BACKEND_URL}${endpoint.path}`);
            console.log(`   ✅ ${endpoint.name}: ${response.status}`);
        } catch (error) {
            console.log(`   ❌ ${endpoint.name}: ${error.response?.status || error.message}`);
        }
    }

    // Test 5: Profile Update Endpoint (without auth)
    console.log('\n5. Testing Profile Update Endpoint (should require auth)...');
    try {
        await axios.put(`${BACKEND_URL}/api/auth/profile`, { test: 'data' });
        console.log(`   ❌ Profile endpoint should require authentication`);
    } catch (error) {
        if (error.response?.status === 401) {
            console.log(`   ✅ Profile endpoint correctly requires authentication`);
        } else {
            console.log(`   ⚠️ Unexpected response: ${error.response?.status || error.message}`);
        }
    }

    console.log('\n🎉 Local Setup Test Complete!\n');
    
    console.log('📋 Summary:');
    console.log('   🔧 Backend should be running on http://localhost:5000');
    console.log('   🎨 Frontend should be running on http://localhost:5173');
    console.log('   🔗 API endpoints accessible at http://localhost:5000/api');
    console.log('   🌐 CORS configured for localhost:5173');
    
    console.log('\n💡 Next Steps:');
    console.log('   1. Start backend: cd server && npm run dev');
    console.log('   2. Start frontend: cd client && npm run dev');
    console.log('   3. Test profile updates with the debug scripts');
    console.log('   4. Check browser dev tools for any CORS issues');
}

// Run the test
testLocalSetup().catch(error => {
    console.error('❌ Test script error:', error.message);
});