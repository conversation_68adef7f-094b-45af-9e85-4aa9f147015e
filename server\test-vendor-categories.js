const axios = require('axios');

async function testVendorCategories() {
  try {
    console.log('🧪 Testing Vendor Categories API...');
    
    // You'll need to replace this with a real vendor token
    // For now, let's test without auth to see if the route exists
    const baseUrl = 'http://localhost:5000/api/vendor/categories';
    
    console.log(`📡 Making request to: ${baseUrl}`);
    
    try {
      const response = await axios.get(baseUrl);
      console.log('✅ Vendor categories API works!');
      console.log('Categories count:', response.data.data.categories.length);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Route exists but requires authentication (expected)');
        console.log('Response:', error.response.data);
      } else {
        console.log('❌ Error:', error.response?.status, error.response?.data);
      }
    }
    
  } catch (error) {
    console.error('❌ General error:', error.message);
  }
}

testVendorCategories();