import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://multi-vendor-server-1tb9.onrender.com/api';

// Create axios instance with default config
const vendorApi = axios.create({
  baseURL: `${API_BASE_URL}/vendor`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
vendorApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle response errors
vendorApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Products APIs
export const productsApi = {
  // Get vendor's products with pagination and filters
  getProducts: (params) => vendorApi.get('/products', { params }),
  
  // Get single product by ID
  getProduct: (id) => vendorApi.get(`/products/${id}`),
  
  // Create new product with images
  createProduct: (formData) => {
    return vendorApi.post('/products', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Update product with optional images
  updateProduct: (id, formData) => {
    return vendorApi.put(`/products/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Delete product
  deleteProduct: (id) => vendorApi.delete(`/products/${id}`),
  
  // Update product stock
  updateStock: (id, quantity, operation = 'set') => 
    vendorApi.patch(`/products/${id}/stock`, { quantity, operation }),
  
  // Bulk operations on products
  bulkOperation: (productIds, action) => 
    vendorApi.post('/products/bulk', { productIds, action }),
  
  // Remove product image
  removeImage: (id, imageUrl) => 
    vendorApi.delete(`/products/${id}/images`, { data: { imageUrl } }),
  
  // Get product statistics
  getStats: () => vendorApi.get('/products/stats'),
};

// Dashboard APIs
export const dashboardApi = {
  getStats: () => vendorApi.get('/dashboard/stats'),
  getAnalytics: (params) => vendorApi.get('/analytics', { params }),
};

// Orders APIs
export const ordersApi = {
  getOrders: (params) => vendorApi.get('/orders', { params }),
  getOrder: (id) => vendorApi.get(`/orders/${id}`),
  updateOrderStatus: (id, status, note) => 
    vendorApi.patch(`/orders/${id}/status`, { status, note }),
  updateShipping: (id, trackingNumber, carrier) => 
    vendorApi.patch(`/orders/${id}/shipping`, { trackingNumber, carrier }),
};

// Store/Profile APIs
export const storeApi = {
  getProfile: () => vendorApi.get('/store/profile'),
  updateProfile: (data) => vendorApi.put('/store/profile', data),
  updateSettings: (data) => vendorApi.put('/store/settings', data),
};

// Categories API (vendor endpoint for getting categories)
export const categoriesApi = {
  getCategories: () => vendorApi.get('/categories'),
};

export default vendorApi;