const nodemailer = require('nodemailer');
const crypto = require('crypto');

class EmailService {
    constructor() {
        const port = parseInt(process.env.SMTP_PORT) || 587;
        this.transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: port,
            secure: port === 465, // true for 465, false for other ports
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            },
            tls: {
                rejectUnauthorized: false // Allow self-signed certificates
            }
        });
    }

    async sendEmail(to, subject, html, text = null) {
        try {
            const mailOptions = {
                from: `"${process.env.APP_NAME || 'Alicartify'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
                to,
                subject,
                html,
                text: text || this.htmlToText(html)
            };

            const result = await this.transporter.sendMail(mailOptions);
            console.log('Em<PERSON> sent successfully:', result.messageId);
            return { success: true, messageId: result.messageId };
        } catch (error) {
            console.error('Email sending failed:', error);
            return { success: false, error: error.message };
        }
    }

    async sendVerificationEmail(user, token) {
        const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email?token=${token}&email=${user.email}`;
        
        const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Email Verification</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #f97316, #ea580c); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #f97316; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Welcome to ${process.env.APP_NAME || 'Alicartify'}!</h1>
                    </div>
                    <div class="content">
                        <h2>Hello ${user.fullName || user.email}!</h2>
                        <p>Thank you for signing up as a ${user.userType}. To complete your registration, please verify your email address by clicking the button below:</p>
                        
                        <div style="text-align: center;">
                            <a href="${verificationUrl}" class="button">Verify Email Address</a>
                        </div>
                        
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
                        
                        <p><strong>This verification link will expire in 24 hours.</strong></p>
                        
                        <p>If you didn't create an account with us, please ignore this email.</p>
                        
                        <p>Best regards,<br>The ${process.env.APP_NAME || 'Alicartify'} Team</p>
                    </div>
                    <div class="footer">
                        <p>© ${new Date().getFullYear()} ${process.env.APP_NAME || 'Alicartify'}. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        return await this.sendEmail(
            user.email,
            'Verify Your Email Address',
            html
        );
    }

    async sendPasswordResetEmail(user, token) {
        const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/reset-password?token=${token}&email=${user.email}`;
        
        const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Password Reset</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                    .warning { background: #fef3cd; border: 1px solid #fecaca; padding: 15px; border-radius: 5px; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Password Reset Request</h1>
                    </div>
                    <div class="content">
                        <h2>Hello ${user.fullName || user.email}!</h2>
                        <p>We received a request to reset your password for your ${process.env.APP_NAME || 'Alicartify'} account.</p>
                        
                        <div class="warning">
                            <strong>⚠️ Security Notice:</strong> If you didn't request this password reset, please ignore this email and consider changing your password as a precaution.
                        </div>
                        
                        <p>To reset your password, click the button below:</p>
                        
                        <div style="text-align: center;">
                            <a href="${resetUrl}" class="button">Reset Password</a>
                        </div>
                        
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style="word-break: break-all; color: #666;">${resetUrl}</p>
                        
                        <p><strong>This reset link will expire in 10 minutes for security reasons.</strong></p>
                        
                        <p>For your security:</p>
                        <ul>
                            <li>This link can only be used once</li>
                            <li>Choose a strong, unique password</li>
                            <li>Don't share your password with anyone</li>
                        </ul>
                        
                        <p>Best regards,<br>The ${process.env.APP_NAME || 'Alicartify'} Team</p>
                    </div>
                    <div class="footer">
                        <p>© ${new Date().getFullYear()} ${process.env.APP_NAME || 'Alicartify'}. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        return await this.sendEmail(
            user.email,
            'Reset Your Password',
            html
        );
    }

    async sendWelcomeEmail(user) {
        const loginUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth`;
        
        const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Welcome!</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                    .feature { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #10b981; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 Welcome to ${process.env.APP_NAME || 'Alicartify'}!</h1>
                    </div>
                    <div class="content">
                        <h2>Hello ${user.fullName || user.email}!</h2>
                        <p>Congratulations! Your email has been verified and your ${user.userType} account is now active.</p>
                        
                        ${user.userType === 'vendor' ? `
                        <div class="feature">
                            <h3>🏪 Vendor Account Features:</h3>
                            <ul>
                                <li>Create and manage your product catalog</li>
                                <li>Track orders and inventory</li>
                                <li>Access vendor dashboard and analytics</li>
                                <li>Manage customer communications</li>
                            </ul>
                            <p><strong>Note:</strong> Your vendor account is pending approval. You'll receive another email once it's approved.</p>
                        </div>
                        ` : `
                        <div class="feature">
                            <h3>🛍️ Customer Account Features:</h3>
                            <ul>
                                <li>Browse thousands of products</li>
                                <li>Track your orders</li>
                                <li>Save favorites and wishlists</li>
                                <li>Secure checkout and payment</li>
                            </ul>
                        </div>
                        `}
                        
                        <div style="text-align: center;">
                            <a href="${loginUrl}" class="button">Start Shopping</a>
                        </div>
                        
                        <p>If you have any questions or need assistance, don't hesitate to contact our support team.</p>
                        
                        <p>Happy ${user.userType === 'vendor' ? 'selling' : 'shopping'}!<br>The ${process.env.APP_NAME || 'Alicartify'} Team</p>
                    </div>
                    <div class="footer">
                        <p>© ${new Date().getFullYear()} ${process.env.APP_NAME || 'Alicartify'}. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        return await this.sendEmail(
            user.email,
            `Welcome to ${process.env.APP_NAME || 'Alicartify'}!`,
            html
        );
    }

    async sendVendorApprovalEmail(user) {
        const dashboardUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/vendor/dashboard`;
        
        const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Vendor Account Approved!</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 Vendor Account Approved!</h1>
                    </div>
                    <div class="content">
                        <h2>Congratulations ${user.businessName || user.fullName}!</h2>
                        <p>Great news! Your vendor account has been approved and you can now start selling on our platform.</p>
                        
                        <p>You now have access to:</p>
                        <ul>
                            <li>Vendor Dashboard</li>
                            <li>Product Management</li>
                            <li>Order Processing</li>
                            <li>Sales Analytics</li>
                            <li>Customer Management</li>
                        </ul>
                        
                        <div style="text-align: center;">
                            <a href="${dashboardUrl}" class="button">Access Vendor Dashboard</a>
                        </div>
                        
                        <p>We're excited to have you as part of our vendor community!</p>
                        
                        <p>Best regards,<br>The ${process.env.APP_NAME || 'Alicartify'} Team</p>
                    </div>
                    <div class="footer">
                        <p>© ${new Date().getFullYear()} ${process.env.APP_NAME || 'Alicartify'}. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        return await this.sendEmail(
            user.email,
            'Your Vendor Account Has Been Approved!',
            html
        );
    }

    htmlToText(html) {
        return html
            .replace(/<[^>]*>/g, '')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .trim();
    }
}

module.exports = new EmailService();