import React from 'react';
import { UserOutlined, ShopOutlined } from '@ant-design/icons';

const AuthSegment = ({ value, onChange, className = "" }) => {
  const options = [
    {
      label: 'Customer',
      value: 'user',
      icon: UserOutlined,
    },
    {
      label: 'Vendor',
      value: 'vendor',
      icon: ShopOutlined,
    },
  ];

  return (
    <div className={`inline-flex bg-gray-100 rounded-lg p-1 ${className}`}>
      {options.map((option) => {
        const Icon = option.icon;
        const isActive = value === option.value;
        
        return (
          <button
            key={option.value}
            onClick={() => onChange(option.value)}
            className={`
              flex items-center justify-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
              ${isActive 
                ? 'bg-white text-orange-600 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
              }
            `}
          >
            <Icon className="h-4 w-4" />
            <span>{option.label}</span>
          </button>
        );
      })}
    </div>
  );
};

export default AuthSegment;