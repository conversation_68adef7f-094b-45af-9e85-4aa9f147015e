const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test profile update with different content types
async function testProfileUpdate() {
    console.log('🧪 Testing Profile Update Fix...\n');

    // First, login to get a token
    try {
        console.log('1. Logging in to get token...');
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
            email: '<EMAIL>',
            password: 'TestPassword123'
        });

        const token = loginResponse.data.data.token;
        console.log('✅ Login successful, token obtained\n');

        // Test 1: Normal application/json request
        console.log('2. Testing normal application/json request...');
        try {
            const response1 = await axios.put(`${BASE_URL}/auth/profile`, {
                address: '123 Test Street'
            }, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            console.log('✅ application/json request successful:', response1.data.message);
        } catch (error) {
            console.log('❌ application/json request failed:', error.response?.data?.message || error.message);
        }

        // Test 2: text/plain request (simulating the problematic request)
        console.log('\n3. Testing text/plain request...');
        try {
            const response2 = await axios.put(`${BASE_URL}/auth/profile`, 
                JSON.stringify({ address: '456 Fixed Street' }), {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'text/plain;charset=UTF-8'
                }
            });
            console.log('✅ text/plain request successful:', response2.data.message);
        } catch (error) {
            console.log('❌ text/plain request failed:', error.response?.data?.message || error.message);
        }

        // Test 3: Fetch API simulation (like frontend)
        console.log('\n4. Testing fetch-like request...');
        try {
            const response3 = await axios({
                method: 'PUT',
                url: `${BASE_URL}/auth/profile`,
                data: JSON.stringify({ address: '789 Fetch Street' }),
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'text/plain;charset=UTF-8'
                }
            });
            console.log('✅ fetch-like request successful:', response3.data.message);
        } catch (error) {
            console.log('❌ fetch-like request failed:', error.response?.data?.message || error.message);
        }

    } catch (error) {
        console.log('❌ Login failed:', error.response?.data?.message || error.message);
        console.log('Make sure the server is running and you have a test user with email: <EMAIL>');
    }
}

// Run the test
testProfileUpdate().catch(console.error);