import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ProductInfoCard from '../components/ProductInfoCard';
import SuggestedProducts from '../components/SuggestedProducts';
import Header from '../components/Header';
import Footer from '../components/Footer';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const response = await fetch(`https://api.escuelajs.co/api/v1/products/${id}`);
        
        if (!response.ok) {
          throw new Error('Product not found');
        }
        
        const productData = await response.json();
        
        // Transform API data to match our ProductInfoCard component structure
        const transformedProduct = {
          id: productData.id,
          name: productData.title,
          brand: productData.category?.name || "Unknown Brand",
          sku: `SKU-${productData.id}`,
          price: productData.price,
          originalPrice: productData.price * 1.2, // Simulate original price
          discount: Math.floor(Math.random() * 30) + 10, // Random discount 10-40%
          rating: (Math.random() * 2 + 3).toFixed(1), // Random rating 3.0-5.0
          reviewCount: Math.floor(Math.random() * 2000) + 100, // Random review count
          inStock: true,
          stockCount: Math.floor(Math.random() * 50) + 5, // Random stock 5-55
          images: productData.images || [],
          variants: [
            { id: 1, name: "Default", color: "#000000", available: true }
          ],
          badges: ["Free Shipping"],
          description: productData.description || "No description available for this product.",
          specifications: {
            "General": {
              "Category": productData.category?.name || "N/A",
              "Product ID": productData.id.toString(),
              "Availability": "In Stock"
            },
            "Details": {
              "Brand": productData.category?.name || "Unknown",
              "Model": `Model-${productData.id}`,
              "Warranty": "1 Year"
            }
          },
          features: [
            "High Quality Materials",
            "Durable Construction",
            "Modern Design",
            "Easy to Use"
          ],
          shipping: {
            freeShipping: true,
            estimatedDays: "2-3",
            returnPolicy: "30-day return"
          }
        };

        setProduct(transformedProduct);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProduct();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading product details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Product Not Found</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => navigate('/')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Home
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-2">
        <nav className="text-sm text-gray-500">
          <button 
            onClick={() => navigate('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span className="mx-2">/</span>
          <button 
            onClick={() => navigate('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Products
          </button>
          <span className="mx-2">/</span>
          <span className="text-gray-800">{product?.name}</span>
        </nav>
      </div>

      <ProductInfoCard product={product} />
      
      {/* Suggested Products Section */}
      <div className="bg-white">
        <SuggestedProducts 
          currentProductId={id}
          categoryName={product?.brand}
          maxProducts={8}
        />
      </div>
      
      <Footer />
    </div>
  );
};

export default ProductDetailPage;