/**
 * Debug script for the specific user having issues
 * Run with: node debug-specific-user.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function debugSpecificUser() {
    console.log('🔍 Debugging Specific User Issues...\n');

    try {
        // Create a user with the same ID pattern as the problematic user
        const timestamp = Date.now();
        const testUser = {
            firstName: 'Debug',
            lastName: 'User',
            email: `debug.user.${timestamp}@forexru.com`, // Similar domain
            password: 'TestPassword123',
            userType: 'user'
        };

        console.log('1. Creating test user with similar pattern...');
        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
        const token = registerResponse.data.data.token;
        console.log(`   ✅ Created: ${testUser.email}`);

        // Test scenarios that might be causing the 400 error
        const problematicScenarios = [
            {
                name: 'Empty object (most likely cause)',
                data: {},
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            {
                name: 'Empty string body',
                data: '',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            {
                name: 'Missing Content-Type header',
                data: { address: '123 Main St' },
                headers: {
                    'Authorization': `Bearer ${token}`
                    // No Content-Type
                }
            },
            {
                name: 'Wrong Content-Type',
                data: { address: '123 Main St' },
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'text/plain'
                }
            },
            {
                name: 'Malformed JSON (as string)',
                data: '{"address": "123 Main St"', // Missing closing brace
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                raw: true
            },
            {
                name: 'Null body',
                data: null,
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            {
                name: 'Array instead of object',
                data: ['address', '123 Main St'],
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            {
                name: 'String instead of object',
                data: 'address=123 Main St',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        ];

        console.log('\n2. Testing problematic scenarios...\n');

        for (let i = 0; i < problematicScenarios.length; i++) {
            const scenario = problematicScenarios[i];
            console.log(`   ${i + 1}. ${scenario.name}:`);

            try {
                let requestConfig = {
                    method: 'PUT',
                    url: `${BASE_URL}/auth/profile`,
                    headers: scenario.headers
                };

                if (scenario.raw) {
                    // Send raw string data
                    requestConfig.data = scenario.data;
                } else {
                    requestConfig.data = scenario.data;
                }

                const response = await axios(requestConfig);
                console.log(`      ✅ Unexpected success: ${response.status} - ${response.data.message}`);

            } catch (error) {
                if (error.response) {
                    console.log(`      ❌ Error: ${error.response.status} - ${error.response.data.message}`);
                    
                    // Check if this matches the user's error pattern
                    if (error.response.status === 400 && error.response.data.message) {
                        console.log(`      🎯 This might be the issue!`);
                    }
                    
                    if (error.response.data.allowedFields) {
                        console.log(`      📋 Allowed: ${error.response.data.allowedFields.join(', ')}`);
                    }
                    if (error.response.data.receivedFields) {
                        console.log(`      📥 Received: ${error.response.data.receivedFields.join(', ')}`);
                    }
                } else {
                    console.log(`      ❌ Network/Parse error: ${error.message}`);
                    if (error.message.includes('JSON')) {
                        console.log(`      🎯 JSON parsing issue - this could be it!`);
                    }
                }
            }

            console.log('');
        }

        // Test what happens with the exact same user ID pattern
        console.log('3. Testing with MongoDB ObjectId pattern...');
        
        // Get the user's actual ObjectId
        const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        const userId = profileResponse.data.data.user._id;
        console.log(`   User ObjectId: ${userId}`);
        
        // Test a simple update to confirm it works
        try {
            const response = await axios.put(`${BASE_URL}/auth/profile`, {
                address: 'Test Address for ObjectId ' + userId
            }, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log(`   ✅ ObjectId user update works: ${response.data.message}`);
        } catch (error) {
            console.log(`   ❌ ObjectId user update failed: ${error.response?.data?.message || error.message}`);
        }

        console.log('\n4. Summary of findings:');
        console.log('   - Address updates work correctly with proper data');
        console.log('   - 400 errors occur with empty/invalid request bodies');
        console.log('   - The user might be sending empty data or malformed JSON');
        console.log('   - Check the client-side code sending the request');

    } catch (error) {
        console.error('❌ Debug error:', error.response?.data || error.message);
    }
}

// Test with the exact same request pattern that might be failing
async function testClientSideIssues() {
    console.log('\n\n🖥️ Testing Common Client-Side Issues...\n');

    try {
        // Create another test user
        const timestamp = Date.now();
        const testUser = {
            firstName: 'Client',
            lastName: 'Test',
            email: `client.test.${timestamp}@example.com`,
            password: 'TestPassword123',
            userType: 'user'
        };

        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
        const token = registerResponse.data.data.token;

        console.log('Testing common client-side mistakes...\n');

        // Common client-side mistakes
        const clientIssues = [
            {
                name: 'FormData instead of JSON',
                test: async () => {
                    const FormData = require('form-data');
                    const form = new FormData();
                    form.append('address', '123 Main St');
                    
                    return axios.put(`${BASE_URL}/auth/profile`, form, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            ...form.getHeaders()
                        }
                    });
                }
            },
            {
                name: 'URL encoded data',
                test: async () => {
                    return axios.put(`${BASE_URL}/auth/profile`, 'address=123+Main+St', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    });
                }
            },
            {
                name: 'Double JSON stringify',
                test: async () => {
                    const data = JSON.stringify(JSON.stringify({ address: '123 Main St' }));
                    return axios.put(`${BASE_URL}/auth/profile`, data, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                }
            }
        ];

        for (let i = 0; i < clientIssues.length; i++) {
            const issue = clientIssues[i];
            console.log(`${i + 1}. ${issue.name}:`);

            try {
                const response = await issue.test();
                console.log(`   ✅ Unexpected success: ${response.status}`);
            } catch (error) {
                if (error.response) {
                    console.log(`   ❌ Error: ${error.response.status} - ${error.response.data.message}`);
                } else {
                    console.log(`   ❌ Network error: ${error.message}`);
                }
            }

            console.log('');
        }

    } catch (error) {
        console.error('❌ Client test error:', error.response?.data || error.message);
    }
}

// Main execution
async function main() {
    await debugSpecificUser();
    await testClientSideIssues();
    
    console.log('\n🎯 Most Likely Causes of the 400 Error:');
    console.log('   1. Client sending empty request body: {}');
    console.log('   2. Client sending malformed JSON');
    console.log('   3. Missing or wrong Content-Type header');
    console.log('   4. Client-side JavaScript error preventing proper data sending');
    console.log('\n💡 Next Steps:');
    console.log('   1. Check the client-side code making the request');
    console.log('   2. Verify the request payload in browser dev tools');
    console.log('   3. Ensure proper JSON.stringify() usage');
    console.log('   4. Check for JavaScript errors in the client');
}

main();