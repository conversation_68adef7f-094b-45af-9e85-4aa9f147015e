const fs = require('fs');
const path = require('path');

// Backup the original file
const originalFile = path.join(__dirname, 'server', 'src', 'routes', 'authRoutes.js');
const backupFile = path.join(__dirname, 'server', 'src', 'routes', 'authRoutes-backup.js');
const fixedFile = path.join(__dirname, 'server', 'src', 'routes', 'authRoutes-fixed.js');

try {
    // Create backup
    if (fs.existsSync(originalFile)) {
        fs.copyFileSync(originalFile, backupFile);
        console.log('✅ Created backup: authRoutes-backup.js');
    }

    // Replace with fixed version
    if (fs.existsSync(fixedFile)) {
        fs.copyFileSync(fixedFile, originalFile);
        console.log('✅ Replaced authRoutes.js with fixed version');
        console.log('🔧 The duplicate middleware issue has been resolved');
        console.log('📝 Profile updates should now work correctly');
    } else {
        console.log('❌ Fixed file not found');
    }

} catch (error) {
    console.error('❌ Error:', error.message);
}

console.log('\n🚀 Please restart your server to apply the changes:');
console.log('   cd server');
console.log('   npm run dev');
console.log('\n🧪 Then test the profile update again');