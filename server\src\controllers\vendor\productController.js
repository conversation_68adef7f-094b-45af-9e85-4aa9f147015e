const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');
const imageUpload = require('../../middleware/upload/imageUpload');
const NotificationService = require('../../services/notificationService');
const path = require('path');
const fs = require('fs').promises;

/**
 * Create a new product
 */
const createProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Check if vendor is verified
    if (vendor.verification.status !== 'verified') {
      return res.status(403).json({
        success: false,
        message: 'Vendor verification required to add products'
      });
    }

    // Check subscription limits
    const currentProductCount = await Product.countDocuments({ 
      vendor: vendor._id,
      status: { $ne: 'archived' }
    });

    if (currentProductCount >= vendor.subscription.features.maxProducts) {
      return res.status(403).json({
        success: false,
        message: `Product limit reached. Your plan allows ${vendor.subscription.features.maxProducts} products`
      });
    }

    // Validate category exists
    const category = await Category.findById(req.body.category);
    if (!category || category.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'Invalid or inactive category'
      });
    }

    // Validate subcategory if provided
    if (req.body.subcategory) {
      const subcategory = await Category.findById(req.body.subcategory);
      if (!subcategory || subcategory.parent.toString() !== req.body.category) {
        return res.status(400).json({
          success: false,
          message: 'Invalid subcategory for the selected category'
        });
      }
    }

    // Check SKU uniqueness
    const existingSku = await Product.findOne({ sku: req.body.sku.toUpperCase() });
    if (existingSku) {
      return res.status(400).json({
        success: false,
        message: 'SKU already exists'
      });
    }

    // Process uploaded images
    let productImages = [];
    if (req.fileUrls && req.fileUrls.length > 0) {
      productImages = req.fileUrls.map((url, index) => ({
        url,
        alt: req.body.name,
        isPrimary: index === 0,
        order: index
      }));
    }

    // Prepare product data
    const productData = {
      vendor: vendor._id,
      name: req.body.name,
      description: req.body.description,
      shortDescription: req.body.shortDescription,
      category: req.body.category,
      subcategory: req.body.subcategory,
      brand: req.body.brand,
      sku: req.body.sku.toUpperCase(),
      barcode: req.body.barcode,
      images: productImages,
      pricing: {
        basePrice: req.body.pricing.basePrice,
        salePrice: req.body.pricing.salePrice,
        costPrice: req.body.pricing.costPrice,
        currency: req.body.pricing.currency || 'USD',
        taxClass: req.body.pricing.taxClass || 'standard',
        taxRate: req.body.pricing.taxRate || 0
      },
      inventory: {
        trackQuantity: req.body.inventory?.trackQuantity !== false,
        quantity: req.body.inventory?.quantity || 0,
        lowStockThreshold: req.body.inventory?.lowStockThreshold || 5,
        allowBackorders: req.body.inventory?.allowBackorders || false
      },
      specifications: req.body.specifications || {},
      shipping: req.body.shipping || {},
      seo: req.body.seo || {},
      status: req.body.status || 'draft',
      visibility: req.body.visibility || 'public',
      featured: req.body.featured || false,
      tags: req.body.tags || [],
      variants: req.body.variants || [],
      attributes: req.body.attributes || [],
      modifiedBy: vendorId
    };

    // Create product
    const product = new Product(productData);
    await product.save();

    // Update vendor product count
    await vendor.updatePerformance({ newProduct: true });

    // Populate product for response
    await product.populate([
      { path: 'vendor', select: 'businessName' },
      { path: 'category', select: 'name slug' },
      { path: 'subcategory', select: 'name slug' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: {
        product
      }
    });

  } catch (error) {
    console.error('Create product error:', error);

    // Clean up uploaded images on error
    if (req.fileUrls) {
      for (const url of req.fileUrls) {
        try {
          await imageUpload.deleteImage(url);
        } catch (deleteError) {
          console.error('Error deleting image:', deleteError);
        }
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get vendor's products with pagination and filters
 */
const getProducts = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Parse query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search;
    const status = req.query.status;
    const category = req.query.category;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Build filter
    const filter = { vendor: vendor._id };

    if (status) {
      filter.status = status;
    }

    if (category) {
      filter.category = category;
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort object
    const sort = {};
    if (sortBy === 'price') {
      sort['pricing.basePrice'] = sortOrder;
    } else if (sortBy === 'sales') {
      sort['sales.totalSold'] = sortOrder;
    } else {
      sort[sortBy] = sortOrder;
    }

    // Get products with pagination
    const [products, totalProducts] = await Promise.all([
      Product.find(filter)
        .populate('category', 'name slug')
        .populate('subcategory', 'name slug')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalProducts / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single product by ID
 */
const getProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    })
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .populate('subcategory', 'name slug');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: {
        product
      }
    });

  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update product
 */
const updateProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if SKU is being changed and if it's unique
    if (req.body.sku && req.body.sku.toUpperCase() !== product.sku) {
      const existingSku = await Product.findOne({ 
        sku: req.body.sku.toUpperCase(),
        _id: { $ne: productId }
      });
      if (existingSku) {
        return res.status(400).json({
          success: false,
          message: 'SKU already exists'
        });
      }
    }

    // Validate category if being changed
    if (req.body.category && req.body.category !== product.category.toString()) {
      const category = await Category.findById(req.body.category);
      if (!category || category.status !== 'active') {
        return res.status(400).json({
          success: false,
          message: 'Invalid or inactive category'
        });
      }
    }

    // Process new images if uploaded
    let newImages = [];
    if (req.fileUrls && req.fileUrls.length > 0) {
      newImages = req.fileUrls.map((url, index) => ({
        url,
        alt: req.body.name || product.name,
        isPrimary: index === 0 && product.images.length === 0,
        order: product.images.length + index
      }));
    }

    // Prepare update data
    const updateData = {
      ...req.body,
      lastModified: new Date(),
      modifiedBy: vendorId
    };

    // Handle SKU update
    if (updateData.sku) {
      updateData.sku = updateData.sku.toUpperCase();
    }

    // Handle images update
    if (newImages.length > 0) {
      updateData.$push = { images: { $each: newImages } };
    }

    // Update product
    const updatedProduct = await Product.findByIdAndUpdate(
      productId,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .populate('subcategory', 'name slug');

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: {
        product: updatedProduct
      }
    });

  } catch (error) {
    console.error('Update product error:', error);

    // Clean up uploaded images on error
    if (req.fileUrls) {
      for (const url of req.fileUrls) {
        try {
          await imageUpload.deleteImage(url);
        } catch (deleteError) {
          console.error('Error deleting image:', deleteError);
        }
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete product
 */
const deleteProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product has orders (prevent deletion if it has orders)
    const Order = require('../../models/Order');
    const hasOrders = await Order.findOne({ 
      'items.product': productId 
    });

    if (hasOrders) {
      // Archive instead of delete if product has orders
      product.status = 'archived';
      await product.save();

      return res.json({
        success: true,
        message: 'Product archived successfully (has existing orders)'
      });
    }

    // Delete product images
    for (const image of product.images) {
      try {
        await imageUpload.deleteImage(image.url);
      } catch (deleteError) {
        console.error('Error deleting image:', deleteError);
      }
    }

    // Delete product
    await Product.findByIdAndDelete(productId);

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update product stock
 */
const updateStock = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;
    const { quantity, operation = 'set' } = req.body;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Update stock using the model method
    await product.updateStock(quantity, operation);

    res.json({
      success: true,
      message: 'Stock updated successfully',
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku,
          inventory: product.inventory
        }
      }
    });

  } catch (error) {
    console.error('Update stock error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update stock',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Bulk operations on products
 */
const bulkOperation = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { productIds, action } = req.body;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Verify all products belong to vendor
    const products = await Product.find({
      _id: { $in: productIds },
      vendor: vendor._id
    });

    if (products.length !== productIds.length) {
      return res.status(400).json({
        success: false,
        message: 'Some products not found or do not belong to vendor'
      });
    }

    let updateData = {};
    let message = '';

    switch (action) {
      case 'activate':
        updateData = { status: 'active' };
        message = 'Products activated successfully';
        break;
      case 'deactivate':
        updateData = { status: 'inactive' };
        message = 'Products deactivated successfully';
        break;
      case 'archive':
        updateData = { status: 'archived' };
        message = 'Products archived successfully';
        break;
      case 'delete':
        // Check if any product has orders
        const Order = require('../../models/Order');
        const hasOrders = await Order.findOne({
          'items.product': { $in: productIds }
        });

        if (hasOrders) {
          return res.status(400).json({
            success: false,
            message: 'Cannot delete products with existing orders. Use archive instead.'
          });
        }

        // Delete product images
        for (const product of products) {
          for (const image of product.images) {
            try {
              await imageUpload.deleteImage(image.url);
            } catch (deleteError) {
              console.error('Error deleting image:', deleteError);
            }
          }
        }

        await Product.deleteMany({ _id: { $in: productIds } });
        message = 'Products deleted successfully';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    if (action !== 'delete') {
      await Product.updateMany(
        { _id: { $in: productIds } },
        { ...updateData, lastModified: new Date(), modifiedBy: vendorId }
      );
    }

    res.json({
      success: true,
      message,
      data: {
        affectedProducts: products.length
      }
    });

  } catch (error) {
    console.error('Bulk operation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk operation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Remove product image
 */
const removeImage = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;
    const imageUrl = req.body.imageUrl;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Find image in product
    const imageIndex = product.images.findIndex(img => img.url === imageUrl);
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Image not found'
      });
    }

    // Check if it's the only image
    if (product.images.length === 1) {
      return res.status(400).json({
        success: false,
        message: 'Cannot remove the only product image'
      });
    }

    // Remove image from product
    const removedImage = product.images[imageIndex];
    product.images.splice(imageIndex, 1);

    // If removed image was primary, make first image primary
    if (removedImage.isPrimary && product.images.length > 0) {
      product.images[0].isPrimary = true;
    }

    // Update image orders
    product.images.forEach((img, index) => {
      img.order = index;
    });

    await product.save();

    // Delete image file
    try {
      await imageUpload.deleteImage(imageUrl);
    } catch (deleteError) {
      console.error('Error deleting image file:', deleteError);
    }

    res.json({
      success: true,
      message: 'Image removed successfully',
      data: {
        product: {
          _id: product._id,
          images: product.images
        }
      }
    });

  } catch (error) {
    console.error('Remove image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove image',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get vendor product statistics
 */
const getProductStats = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Get product statistics
    const stats = await Product.aggregate([
      { $match: { vendor: vendor._id } },
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          activeProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          draftProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          inactiveProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
          },
          outOfStockProducts: {
            $sum: { $cond: [{ $eq: ['$inventory.stockStatus', 'out_of_stock'] }, 1, 0] }
          },
          lowStockProducts: {
            $sum: { $cond: [{ $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }, 1, 0] }
          },
          totalRevenue: { $sum: '$sales.totalRevenue' },
          totalSold: { $sum: '$sales.totalSold' },
          averagePrice: { $avg: '$pricing.basePrice' }
        }
      }
    ]);

    const productStats = stats[0] || {
      totalProducts: 0,
      activeProducts: 0,
      draftProducts: 0,
      inactiveProducts: 0,
      outOfStockProducts: 0,
      lowStockProducts: 0,
      totalRevenue: 0,
      totalSold: 0,
      averagePrice: 0
    };

    // Get top selling products
    const topSellingProducts = await Product.find({ vendor: vendor._id })
      .sort({ 'sales.totalSold': -1 })
      .limit(5)
      .select('name sales.totalSold sales.totalRevenue pricing.basePrice')
      .lean();

    res.json({
      success: true,
      data: {
        stats: productStats,
        topSellingProducts,
        subscriptionLimits: {
          maxProducts: vendor.subscription.features.maxProducts,
          currentProducts: productStats.totalProducts,
          remainingProducts: vendor.subscription.features.maxProducts - productStats.totalProducts
        }
      }
    });

  } catch (error) {
    console.error('Get product stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Submit product for approval
 */
const submitForApproval = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Check if vendor is verified
    if (vendor.verification.status !== 'verified') {
      return res.status(403).json({
        success: false,
        message: 'Vendor must be verified to submit products for approval'
      });
    }

    // Find product
    const product = await Product.findOne({
      _id: productId,
      vendor: vendor._id
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product is in draft status
    if (product.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Only draft products can be submitted for approval'
      });
    }

    // Validate product has required fields
    const requiredFields = ['name', 'description', 'category', 'pricing.basePrice', 'sku'];
    const missingFields = [];

    requiredFields.forEach(field => {
      const fieldParts = field.split('.');
      let value = product;

      for (const part of fieldParts) {
        value = value?.[part];
      }

      if (!value) {
        missingFields.push(field);
      }
    });

    if (product.images.length === 0) {
      missingFields.push('images');
    }

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Product is missing required fields',
        data: { missingFields }
      });
    }

    await product.submitForApproval(vendorId);

    // Send notification to admins
    try {
      await NotificationService.notifyProductApprovalRequest(product, vendor);
    } catch (notificationError) {
      console.error('Failed to send approval request notification:', notificationError);
      // Don't fail the submission if notification fails
    }

    res.json({
      success: true,
      message: 'Product submitted for approval successfully',
      data: { product }
    });

  } catch (error) {
    console.error('Submit for approval error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit product for approval',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get product approval history
 */
const getApprovalHistory = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({
      _id: productId,
      vendor: vendor._id
    })
      .populate('approval.history.performedBy', 'firstName lastName role')
      .populate('approval.reviewedBy', 'firstName lastName role')
      .select('approval');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: { approval: product.approval }
    });

  } catch (error) {
    console.error('Get approval history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch approval history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createProduct,
  getProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  updateStock,
  bulkOperation,
  removeImage,
  getProductStats,
  submitForApproval,
  getApprovalHistory
};