const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

const userSchema = new mongoose.Schema({
    // Basic Information
    firstName: {
        type: String,
        required: function() {
            return this.userType === 'user';
        },
        trim: true,
        maxlength: 50
    },
    lastName: {
        type: String,
        required: function() {
            return this.userType === 'user';
        },
        trim: true,
        maxlength: 50
    },
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    password: {
        type: String,
        required: function() {
            return !this.socialAuth.isEnabled;
        },
        minlength: 8,
        select: false // Don't include password in queries by default
    },
    
    // User Type and Role
    userType: {
        type: String,
        enum: ['user', 'vendor', 'admin'],
        default: 'user',
        required: true
    },
    role: {
        type: String,
        enum: ['customer', 'vendor', 'admin', 'super_admin'],
        default: function() {
            return this.userType === 'vendor' ? 'vendor' : 'customer';
        }
    },
    
    // Vendor-specific fields
    businessName: {
        type: String,
        required: function() {
            return this.userType === 'vendor';
        },
        trim: true,
        maxlength: 100
    },
    businessType: {
        type: String,
        enum: ['retail', 'wholesale', 'manufacturer', 'dropshipping', 'services', 'other'],
        required: function() {
            return this.userType === 'vendor';
        }
    },
    contactPerson: {
        type: String,
        required: function() {
            return this.userType === 'vendor';
        },
        trim: true,
        maxlength: 100
    },
    businessAddress: {
        street: String,
        city: String,
        state: String,
        zipCode: String,
        country: String
    },
    taxId: {
        type: String,
        trim: true
    },
    
    // Profile Information
    avatar: {
        type: String,
        default: null
    },
    dateOfBirth: {
        type: Date
    },
    gender: {
        type: String,
        enum: ['male', 'female', 'other', 'prefer_not_to_say']
    },
    
    // Address Information
    address: {
        type: String,
        trim: true,
        maxlength: 200
    },
    city: {
        type: String,
        trim: true,
        maxlength: 100
    },
    state: {
        type: String,
        trim: true,
        maxlength: 100
    },
    zipCode: {
        type: String,
        trim: true,
        maxlength: 20
    },
    country: {
        type: String,
        trim: true,
        maxlength: 100,
        default: 'United States'
    },
    
    // Statistics
    statistics: {
        totalOrders: {
            type: Number,
            default: 0
        },
        totalSpent: {
            type: Number,
            default: 0
        },
        totalSaved: {
            type: Number,
            default: 0
        },
        favoriteCategories: [{
            category: String,
            count: Number
        }],
        lastOrderDate: Date,
        averageOrderValue: {
            type: Number,
            default: 0
        }
    },
    
    // Account Status
    isActive: {
        type: Boolean,
        default: true
    },
    isBlocked: {
        type: Boolean,
        default: false
    },
    blockReason: {
        type: String,
        trim: true
    },
    
    // Email Verification
    emailVerification: {
        isVerified: {
            type: Boolean,
            default: false
        },
        verificationToken: {
            type: String,
            select: false
        },
        verificationTokenExpires: {
            type: Date,
            select: false
        },
        verifiedAt: {
            type: Date
        }
    },
    
        
    // Password Reset
    passwordReset: {
        token: {
            type: String,
            select: false
        },
        tokenExpires: {
            type: Date,
            select: false
        },
        lastResetAt: {
            type: Date
        }
    },
    
    // Two-Factor Authentication
    twoFactorAuth: {
        isEnabled: {
            type: Boolean,
            default: false
        },
        secret: {
            type: String,
            select: false
        },
        backupCodes: [{
            code: String,
            used: {
                type: Boolean,
                default: false
            }
        }],
        lastUsedAt: Date
    },
    
    // Social Authentication
    socialAuth: {
        isEnabled: {
            type: Boolean,
            default: false
        },
        providers: [{
            provider: {
                type: String,
                enum: ['google', 'facebook', 'github', 'apple']
            },
            providerId: String,
            email: String,
            connectedAt: {
                type: Date,
                default: Date.now
            }
        }]
    },
    
    // Security and Login Tracking
    security: {
        lastLogin: {
            type: Date
        },
        lastLoginIP: {
            type: String
        },
        loginAttempts: {
            type: Number,
            default: 0
        },
        lockUntil: {
            type: Date
        },
        passwordChangedAt: {
            type: Date
        },
        securityQuestions: [{
            question: String,
            answer: String
        }]
    },
    
    // Preferences
    preferences: {
        language: {
            type: String,
            default: 'en'
        },
        timezone: {
            type: String,
            default: 'UTC'
        },
        currency: {
            type: String,
            default: 'USD'
        },
        notifications: {
            email: {
                marketing: {
                    type: Boolean,
                    default: true
                },
                orderUpdates: {
                    type: Boolean,
                    default: true
                },
                security: {
                    type: Boolean,
                    default: true
                }
            },
            sms: {
                marketing: {
                    type: Boolean,
                    default: false
                },
                orderUpdates: {
                    type: Boolean,
                    default: true
                },
                security: {
                    type: Boolean,
                    default: true
                }
            },
            push: {
                marketing: {
                    type: Boolean,
                    default: true
                },
                orderUpdates: {
                    type: Boolean,
                    default: true
                },
                security: {
                    type: Boolean,
                    default: true
                }
            }
        }
    },
    
    // Vendor-specific status
    vendorStatus: {
        isApproved: {
            type: Boolean,
            default: false
        },
        approvedAt: Date,
        approvedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        rejectionReason: String,
        documents: [{
            type: {
                type: String,
                enum: ['business_license', 'tax_certificate', 'identity_proof', 'address_proof', 'other']
            },
            url: String,
            uploadedAt: {
                type: Date,
                default: Date.now
            },
            verified: {
                type: Boolean,
                default: false
            }
        }]
    },
    
    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    lastActiveAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better performance (email index is automatically created by unique: true)
userSchema.index({ userType: 1 });
userSchema.index({ 'emailVerification.isVerified': 1 });
userSchema.index({ 'vendorStatus.isApproved': 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ lastActiveAt: -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
    if (this.userType === 'user') {
        return `${this.firstName} ${this.lastName}`.trim();
    } else if (this.userType === 'vendor') {
        return this.businessName || this.contactPerson;
    }
    return this.email;
});

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
    return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
    // Only hash the password if it has been modified (or is new)
    if (!this.isModified('password')) return next();
    
    try {
        // Hash password with cost of 12
        const salt = await bcrypt.genSalt(12);
        this.password = await bcrypt.hash(this.password, salt);
        
        // Update password changed timestamp
        this.security.passwordChangedAt = new Date();
        
        next();
    } catch (error) {
        next(error);
    }
});

// Pre-save middleware to update timestamps
userSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    if (this.isNew) {
        this.createdAt = new Date();
        this.lastActiveAt = new Date();
    }
    next();
});

// Instance method to check password
userSchema.methods.comparePassword = async function(candidatePassword) {
    if (!this.password) return false;
    return await bcrypt.compare(candidatePassword, this.password);
};

// Instance method to generate email verification token
userSchema.methods.generateEmailVerificationToken = function() {
    const token = crypto.randomBytes(32).toString('hex');
    this.emailVerification.verificationToken = crypto
        .createHash('sha256')
        .update(token)
        .digest('hex');
    this.emailVerification.verificationTokenExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours
    
    return token;
};

// Instance method to generate password reset token
userSchema.methods.generatePasswordResetToken = function() {
    const resetToken = crypto.randomBytes(32).toString('hex');
    this.passwordReset.token = crypto
        .createHash('sha256')
        .update(resetToken)
        .digest('hex');
    this.passwordReset.tokenExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
    
    return resetToken;
};


// Instance method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
    // If we have a previous lock that has expired, restart at 1
    if (this.security.lockUntil && this.security.lockUntil < Date.now()) {
        return this.updateOne({
            $unset: { 'security.lockUntil': 1 },
            $set: { 'security.loginAttempts': 1 }
        });
    }
    
    const updates = { $inc: { 'security.loginAttempts': 1 } };
    
    // Lock account after 5 failed attempts for 2 hours
    if (this.security.loginAttempts + 1 >= 5 && !this.isLocked) {
        updates.$set = { 'security.lockUntil': Date.now() + 2 * 60 * 60 * 1000 };
    }
    
    return this.updateOne(updates);
};

// Instance method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
    return this.updateOne({
        $unset: {
            'security.loginAttempts': 1,
            'security.lockUntil': 1
        }
    });
};

// Static method to find by email
userSchema.statics.findByEmail = function(email) {
    return this.findOne({ email: email.toLowerCase() });
};

// Static method to find verified users
userSchema.statics.findVerifiedUsers = function() {
    return this.find({ 'emailVerification.isVerified': true });
};

// Static method to find pending vendors
userSchema.statics.findPendingVendors = function() {
    return this.find({ 
        userType: 'vendor',
        'vendorStatus.isApproved': false 
    });
};

const User = mongoose.models.User || mongoose.model('User', userSchema);

module.exports = User;