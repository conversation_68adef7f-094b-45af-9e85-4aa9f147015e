/**
 * Simple test for profile update without external dependencies
 */

require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testProfileUpdate() {
    console.log('🧪 Testing Profile Update Fix\n');
    
    // First, let's test with JSON data (should work)
    console.log('1️⃣ Testing with JSON data...');
    try {
        const response = await axios.put(`${BASE_URL}/auth/profile`, {
            address: '123 Test Street',
            city: 'Test City'
        }, {
            headers: {
                'Authorization': 'Bearer test-token',
                'Content-Type': 'application/json'
            }
        });
        console.log('✅ JSON request successful');
    } catch (error) {
        if (error.response?.status === 401) {
            console.log('✅ JSON request processed (401 expected - no valid token)');
        } else {
            console.log('❌ JSON request failed:', error.response?.status, error.response?.data?.message);
        }
    }
    
    // Test with form data using URLSearchParams (built-in)
    console.log('\n2️⃣ Testing with form data...');
    try {
        const formData = new URLSearchParams();
        formData.append('address', '456 Form Street');
        formData.append('city', 'Form City');
        
        const response = await axios.put(`${BASE_URL}/auth/profile`, formData, {
            headers: {
                'Authorization': 'Bearer test-token',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });
        console.log('✅ Form data request successful');
    } catch (error) {
        if (error.response?.status === 401) {
            console.log('✅ Form data request processed (401 expected - no valid token)');
        } else {
            console.log('❌ Form data request failed:', error.response?.status, error.response?.data?.message);
        }
    }
    
    console.log('\n💡 Both request types should be processed by the server now.');
    console.log('   The 401 errors are expected since we\'re using a test token.');
    console.log('   The important thing is that the server doesn\'t return undefined body.');
}

testProfileUpdate().catch(console.error);