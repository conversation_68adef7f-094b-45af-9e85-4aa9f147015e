import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Input,
  Descriptions,
  Avatar,
  message
} from 'antd';
import {
  ShoppingCartOutlined,
  EyeOutlined,
  SearchOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TruckOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const OrdersManagement = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Sample data - replace with actual API calls
  const sampleOrders = [
    {
      id: 'ORD-001',
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      vendorName: 'TechStore Pro',
      products: [
        { name: 'iPhone 15 Pro', quantity: 1, price: 999.99 },
        { name: 'AirPods Pro', quantity: 1, price: 249.99 }
      ],
      totalAmount: 1249.98,
      status: 'delivered',
      orderDate: '2024-03-15',
      deliveryDate: '2024-03-18',
      shippingAddress: '123 Main St, New York, NY 10001',
      paymentMethod: 'Credit Card',
      trackingNumber: 'TRK123456789'
    },
    {
      id: 'ORD-002',
      customerName: 'Jane Smith',
      customerEmail: '<EMAIL>',
      vendorName: 'Fashion Hub',
      products: [
        { name: 'Designer Dress', quantity: 2, price: 89.99 },
        { name: 'Handbag', quantity: 1, price: 159.99 }
      ],
      totalAmount: 339.97,
      status: 'shipped',
      orderDate: '2024-03-20',
      deliveryDate: null,
      shippingAddress: '456 Oak Ave, Los Angeles, CA 90210',
      paymentMethod: 'PayPal',
      trackingNumber: 'TRK987654321'
    },
    {
      id: 'ORD-003',
      customerName: 'Bob Wilson',
      customerEmail: '<EMAIL>',
      vendorName: 'Home & Garden',
      products: [
        { name: 'Garden Tools Set', quantity: 1, price: 79.99 },
        { name: 'Plant Pots', quantity: 3, price: 15.99 }
      ],
      totalAmount: 127.96,
      status: 'processing',
      orderDate: '2024-03-22',
      deliveryDate: null,
      shippingAddress: '789 Pine St, Chicago, IL 60601',
      paymentMethod: 'Credit Card',
      trackingNumber: null
    },
    {
      id: 'ORD-004',
      customerName: 'Alice Brown',
      customerEmail: '<EMAIL>',
      vendorName: 'Sports World',
      products: [
        { name: 'Running Shoes', quantity: 1, price: 129.99 }
      ],
      totalAmount: 129.99,
      status: 'pending',
      orderDate: '2024-03-25',
      deliveryDate: null,
      shippingAddress: '321 Elm St, Miami, FL 33101',
      paymentMethod: 'Credit Card',
      trackingNumber: null
    },
    {
      id: 'ORD-005',
      customerName: 'Mike Johnson',
      customerEmail: '<EMAIL>',
      vendorName: 'TechStore Pro',
      products: [
        { name: 'Laptop', quantity: 1, price: 1299.99 }
      ],
      totalAmount: 1299.99,
      status: 'cancelled',
      orderDate: '2024-03-10',
      deliveryDate: null,
      shippingAddress: '654 Maple Dr, Seattle, WA 98101',
      paymentMethod: 'Credit Card',
      trackingNumber: null
    }
  ];

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      // Simulate API call
      setTimeout(() => {
        setOrders(sampleOrders);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('Failed to fetch orders');
      setLoading(false);
    }
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setDetailModalVisible(true);
  };

  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      setOrders(orders.map(order =>
        order.id === orderId
          ? { ...order, status: newStatus }
          : order
      ));
      message.success(`Order status updated to ${newStatus}`);
    } catch (error) {
      message.error('Failed to update order status');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'processing':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockCircleOutlined />;
      case 'processing':
        return <ClockCircleOutlined />;
      case 'shipped':
        return <TruckOutlined />;
      case 'delivered':
        return <CheckCircleOutlined />;
      case 'cancelled':
        return <CloseCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = 
      order.id.toLowerCase().includes(searchText.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      order.vendorName.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      render: (id) => <strong>{id}</strong>,
    },
    {
      title: 'Customer',
      dataIndex: 'customerName',
      key: 'customerName',
      render: (name, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{name}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{record.customerEmail}</div>
        </div>
      ),
    },
    {
      title: 'Vendor',
      dataIndex: 'vendorName',
      key: 'vendorName',
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => `$${amount.toFixed(2)}`,
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Order Date',
      dataIndex: 'orderDate',
      key: 'orderDate',
      sorter: (a, b) => new Date(a.orderDate) - new Date(b.orderDate),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewOrder(record)}
          >
            View Details
          </Button>
          <Select
            size="small"
            value={record.status}
            onChange={(value) => handleUpdateOrderStatus(record.id, value)}
            style={{ width: 120 }}
          >
            <Option value="pending">Pending</Option>
            <Option value="processing">Processing</Option>
            <Option value="shipped">Shipped</Option>
            <Option value="delivered">Delivered</Option>
            <Option value="cancelled">Cancelled</Option>
          </Select>
        </Space>
      ),
    },
  ];

  const totalOrders = orders.length;
  const pendingOrders = orders.filter(order => order.status === 'pending').length;
  const deliveredOrders = orders.filter(order => order.status === 'delivered').length;
  const totalRevenue = orders
    .filter(order => order.status === 'delivered')
    .reduce((sum, order) => sum + order.totalAmount, 0);

  return (
    <div>
      <Title level={2}>Orders Management</Title>
      
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Orders"
              value={totalOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Pending Orders"
              value={pendingOrders}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Delivered Orders"
              value={deliveredOrders}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={totalRevenue}
              prefix={<DollarOutlined />}
              formatter={(value) => `$${value.toFixed(2)}`}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 16 }}>
          <Space>
            <Input
              placeholder="Search orders..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 150 }}
            >
              <Option value="all">All Status</Option>
              <Option value="pending">Pending</Option>
              <Option value="processing">Processing</Option>
              <Option value="shipped">Shipped</Option>
              <Option value="delivered">Delivered</Option>
              <Option value="cancelled">Cancelled</Option>
            </Select>
          </Space>
          <RangePicker />
        </div>

        <Table
          columns={columns}
          dataSource={filteredOrders}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} orders`,
          }}
        />
      </Card>

      {/* Order Detail Modal */}
      <Modal
        title={`Order Details - ${selectedOrder?.id}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Order ID">{selectedOrder.id}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedOrder.status)} icon={getStatusIcon(selectedOrder.status)}>
                  {selectedOrder.status.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Customer">{selectedOrder.customerName}</Descriptions.Item>
              <Descriptions.Item label="Email">{selectedOrder.customerEmail}</Descriptions.Item>
              <Descriptions.Item label="Vendor">{selectedOrder.vendorName}</Descriptions.Item>
              <Descriptions.Item label="Payment Method">{selectedOrder.paymentMethod}</Descriptions.Item>
              <Descriptions.Item label="Order Date">{selectedOrder.orderDate}</Descriptions.Item>
              <Descriptions.Item label="Delivery Date">{selectedOrder.deliveryDate || 'Not delivered'}</Descriptions.Item>
              <Descriptions.Item label="Tracking Number" span={2}>
                {selectedOrder.trackingNumber || 'Not assigned'}
              </Descriptions.Item>
              <Descriptions.Item label="Shipping Address" span={2}>
                {selectedOrder.shippingAddress}
              </Descriptions.Item>
            </Descriptions>

            <Title level={4} style={{ marginTop: 24, marginBottom: 16 }}>Products</Title>
            <Table
              dataSource={selectedOrder.products}
              pagination={false}
              size="small"
              columns={[
                {
                  title: 'Product',
                  dataIndex: 'name',
                  key: 'name',
                },
                {
                  title: 'Quantity',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  align: 'center',
                },
                {
                  title: 'Price',
                  dataIndex: 'price',
                  key: 'price',
                  render: (price) => `$${price.toFixed(2)}`,
                },
                {
                  title: 'Total',
                  key: 'total',
                  render: (_, record) => `$${(record.quantity * record.price).toFixed(2)}`,
                },
              ]}
              summary={() => (
                <Table.Summary.Row>
                  <Table.Summary.Cell colSpan={3}><strong>Total Amount</strong></Table.Summary.Cell>
                  <Table.Summary.Cell>
                    <strong>${selectedOrder.totalAmount.toFixed(2)}</strong>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrdersManagement;