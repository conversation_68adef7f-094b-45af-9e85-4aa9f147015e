/**
 * Test script to debug address update issues
 * Run with: node test-address-update.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAddressUpdate() {
    console.log('🏠 Testing Address Update Issues...\n');

    try {
        // Step 1: Create a test user or login with existing
        console.log('1. Setting up test user...');
        
        let token = null;
        const testEmail = '<EMAIL>';
        
        // Try to login with the specific user from logs
        try {
            // Try common passwords
            const passwords = ['password', 'Password123', 'test123', 'TestPassword123'];
            
            for (const password of passwords) {
                try {
                    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
                        email: testEmail,
                        password: password
                    });
                    
                    if (loginResponse.data.success) {
                        token = loginResponse.data.data.token;
                        console.log(`   ✅ Logged in with existing user: ${testEmail}`);
                        break;
                    }
                } catch (error) {
                    continue; // Try next password
                }
            }
        } catch (error) {
            // User doesn't exist or wrong password
        }
        
        // If login failed, create a new test user
        if (!token) {
            console.log('   Creating new test user...');
            const timestamp = Date.now();
            const testUser = {
                firstName: 'Address',
                lastName: 'Test',
                email: `address.test.${timestamp}@example.com`,
                password: 'TestPassword123',
                userType: 'user'
            };
            
            const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
            token = registerResponse.data.data.token;
            console.log(`   ✅ Created test user: ${testUser.email}`);
        }

        // Step 2: Get current profile
        console.log('\n2. Getting current profile...');
        const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        const user = profileResponse.data.data.user;
        console.log(`   📧 Email: ${user.email}`);
        console.log(`   👤 Name: ${user.firstName} ${user.lastName}`);
        console.log(`   🏠 Current address: ${user.address || 'Not set'}`);
        console.log(`   🏙️ Current city: ${user.city || 'Not set'}`);

        // Step 3: Test various address update scenarios
        console.log('\n3. Testing address updates...\n');
        
        const addressTests = [
            {
                name: 'Simple address update',
                data: {
                    address: '123 Main Street'
                }
            },
            {
                name: 'Full address update',
                data: {
                    address: '456 Oak Avenue',
                    city: 'New York',
                    state: 'NY',
                    zipCode: '10001',
                    country: 'United States'
                }
            },
            {
                name: 'City only update',
                data: {
                    city: 'Los Angeles'
                }
            },
            {
                name: 'Empty address (should clear)',
                data: {
                    address: ''
                }
            },
            {
                name: 'Mixed profile and address',
                data: {
                    firstName: 'Updated',
                    address: '789 Pine Street',
                    city: 'Chicago'
                }
            },
            {
                name: 'International address',
                data: {
                    address: '10 Downing Street',
                    city: 'London',
                    state: 'England',
                    zipCode: 'SW1A 2AA',
                    country: 'United Kingdom'
                }
            }
        ];

        for (let i = 0; i < addressTests.length; i++) {
            const test = addressTests[i];
            console.log(`   ${i + 1}. ${test.name}:`);
            console.log(`      Data: ${JSON.stringify(test.data)}`);

            try {
                const response = await axios.put(`${BASE_URL}/auth/profile`, test.data, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`      ✅ Success: ${response.data.message}`);
                if (response.data.data.updatedFields) {
                    console.log(`      📝 Updated: ${response.data.data.updatedFields.join(', ')}`);
                }

            } catch (error) {
                if (error.response) {
                    console.log(`      ❌ Error: ${error.response.status} - ${error.response.data.message}`);
                    
                    if (error.response.data.allowedFields) {
                        console.log(`      📋 Allowed: ${error.response.data.allowedFields.join(', ')}`);
                    }
                    if (error.response.data.receivedFields) {
                        console.log(`      📥 Received: ${error.response.data.receivedFields.join(', ')}`);
                    }
                    if (error.response.data.errors) {
                        console.log(`      📝 Validation errors:`);
                        error.response.data.errors.forEach(err => {
                            console.log(`         - ${err.field}: ${err.message}`);
                        });
                    }
                } else {
                    console.log(`      ❌ Network error: ${error.message}`);
                }
            }

            console.log(''); // Empty line
        }

        // Step 4: Get final profile to see what was actually saved
        console.log('4. Getting final profile state...');
        const finalProfile = await axios.get(`${BASE_URL}/auth/profile`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        const finalUser = finalProfile.data.data.user;
        console.log('   📋 Final profile state:');
        console.log(`   📧 Email: ${finalUser.email}`);
        console.log(`   👤 Name: ${finalUser.firstName} ${finalUser.lastName}`);
        console.log(`   🏠 Address: ${finalUser.address || 'Not set'}`);
        console.log(`   🏙️ City: ${finalUser.city || 'Not set'}`);
        console.log(`   🏛️ State: ${finalUser.state || 'Not set'}`);
        console.log(`   📮 ZIP: ${finalUser.zipCode || 'Not set'}`);
        console.log(`   🌍 Country: ${finalUser.country || 'Not set'}`);

        console.log('\n🎉 Address update tests completed!');

    } catch (error) {
        console.error('❌ Test error:', error.response?.data || error.message);
    }
}

// Test with different data formats that might be causing issues
async function testProblematicFormats() {
    console.log('\n🔍 Testing Problematic Data Formats...\n');

    try {
        // Create a test user
        const timestamp = Date.now();
        const testUser = {
            firstName: 'Format',
            lastName: 'Test',
            email: `format.test.${timestamp}@example.com`,
            password: 'TestPassword123',
            userType: 'user'
        };

        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
        const token = registerResponse.data.data.token;

        const problematicTests = [
            {
                name: 'Null values',
                data: {
                    address: null,
                    city: 'Valid City'
                }
            },
            {
                name: 'Undefined values',
                data: {
                    address: undefined,
                    city: 'Valid City'
                }
            },
            {
                name: 'Very long address',
                data: {
                    address: 'A'.repeat(201) // Exceeds 200 char limit
                }
            },
            {
                name: 'Special characters',
                data: {
                    address: '123 Main St. Apt #4B',
                    city: 'São Paulo'
                }
            },
            {
                name: 'Numbers only',
                data: {
                    address: '12345',
                    zipCode: '90210'
                }
            }
        ];

        for (let i = 0; i < problematicTests.length; i++) {
            const test = problematicTests[i];
            console.log(`${i + 1}. ${test.name}:`);

            try {
                const response = await axios.put(`${BASE_URL}/auth/profile`, test.data, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`   ✅ Success: ${response.data.message}`);

            } catch (error) {
                if (error.response) {
                    console.log(`   ❌ Error: ${error.response.status} - ${error.response.data.message}`);
                    if (error.response.data.errors) {
                        error.response.data.errors.forEach(err => {
                            console.log(`      - ${err.field}: ${err.message}`);
                        });
                    }
                } else {
                    console.log(`   ❌ Network error: ${error.message}`);
                }
            }

            console.log('');
        }

    } catch (error) {
        console.error('❌ Problematic format test error:', error.response?.data || error.message);
    }
}

// Main execution
async function main() {
    await testAddressUpdate();
    await testProblematicFormats();
    
    console.log('\n💡 If you\'re still seeing 400 errors:');
    console.log('   1. Check the server logs for the detailed debug output');
    console.log('   2. Verify the request data format');
    console.log('   3. Ensure the Authorization header is correct');
    console.log('   4. Check if the user exists and token is valid');
}

main();