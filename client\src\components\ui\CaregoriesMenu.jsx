import React, { useState, useEffect } from 'react';
import { Card, Modal } from 'antd';
import { StarOutlined, GiftOutlined, AudioOutlined, CrownOutlined, ContactsOutlined, HomeOutlined, CloseOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ProductInfo } from '../../utils/Api';

const CatergoriesMenu = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (isModalOpen) {
      setLoading(true);
      ProductInfo()
        .then(data => {
          setProducts(data.slice(0, 8)); // Limit to 8 products for display
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching products:', error);
          setLoading(false);
        });
    }
  }, [isModalOpen]);

  const categories = [
    { name: 'Categories for you', icon: <StarOutlined /> },
    { name: 'Home Decor & Crafts', icon: <GiftOutlined /> },
    { name: 'Consumer Electronics', icon: <AudioOutlined /> },
    { name: 'Jewelry, Eyewear & Watches', icon: <CrownOutlined /> },
    { name: 'Apparel & Accessories', icon: <ContactsOutlined /> },
    { name: 'Home & Garden', icon: <HomeOutlined /> },
    { name: 'Consumer Electronics', icon: <AudioOutlined /> }
  ];

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
    setIsModalOpen(true);
  };

  const handleProductClick = (productId) => {
    navigate(`/product/${productId}`);
    setIsModalOpen(false);
  };

  return (
    <div className="w-full max-w-full h-full">
      <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full p-0 border-none shadow-none bg-transparent h-full">
        <ul className="space-y-2">
          {categories.map((category, index) => (
            <li
              key={index}
              className="flex items-center justify-between cursor-pointer hover:bg-gray-100 rounded px-2 py-1"
              onClick={() => handleCategoryClick(category)}
            >
              <span className="flex items-center gap-2 text-gray-700">{category.icon} {category.name}</span>
              <span className="text-gray-400">&gt;</span>
            </li>
          ))}
        </ul>
      </Card>

      <Modal
        title={selectedCategory?.name || "Category Products"}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={800}
        closeIcon={<CloseOutlined />}
      >
        <div className="flex flex-col md:flex-row">
          {/* Left side - Categories */}
          <div className="w-full md:w-1/3 border-r border-gray-200 pr-4">
            <ul className="space-y-2">
              {categories.map((category, index) => (
                <li
                  key={index}
                  className={`flex items-center cursor-pointer hover:bg-gray-100 rounded px-2 py-2 ${selectedCategory?.name === category.name ? 'bg-blue-50 text-blue-600' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  <span className="flex items-center gap-2">{category.icon} {category.name}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Right side - Products */}
          <div className="w-full md:w-2/3 pl-4 mt-4 md:mt-0">
            {loading ? (
              <div className="grid grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((_, index) => (
                  <div key={index} className="animate-pulse bg-gray-200 h-40 rounded-lg"></div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                {products.map(product => (
                  <div
                    key={product.id}
                    className="cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-100 rounded-lg overflow-hidden"
                    onClick={() => handleProductClick(product.id)}
                  >
                    <div className="h-32 overflow-hidden">
                      <img
                        src={product.images?.[0] || 'https://via.placeholder.com/200x200?text=No+Image'}
                        alt={product.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.src = 'https://via.placeholder.com/200x200?text=No+Image';
                        }}
                      />
                    </div>
                    <div className="p-2">
                      <p className="text-sm font-medium text-gray-800 truncate">{product.title}</p>
                      <p className="text-sm font-bold text-red-600">${product.price}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CatergoriesMenu;