#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./server/schema/userSchema');

// Get email from command line arguments
const email = process.argv[2];

if (!email) {
    console.log('Usage: node user-lookup.js <email>');
    console.log('Example: node user-lookup.js <EMAIL>');
    process.exit(1);
}

// Connect to database and fetch user
async function lookupUser(userEmail) {
    try {
        // Connect to MongoDB
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
        await mongoose.connect(mongoURI);
        console.log('✅ Connected to database');

        // Find user
        console.log(`🔍 Looking up user: ${userEmail}`);
        const user = await User.findByEmail(userEmail);

        if (!user) {
            console.log('❌ User not found');
            return;
        }

        // Display user information
        console.log('\n📋 USER DETAILS:');
        console.log('================');
        console.log(`ID: ${user._id}`);
        console.log(`Email: ${user.email}`);
        console.log(`Type: ${user.userType}`);
        console.log(`Role: ${user.role}`);
        
        if (user.userType === 'user') {
            console.log(`Name: ${user.firstName} ${user.lastName}`);
        } else if (user.userType === 'vendor') {
            console.log(`Business: ${user.businessName}`);
            console.log(`Business Type: ${user.businessType}`);
            console.log(`Contact Person: ${user.contactPerson}`);
        }

        console.log(`Status: ${user.isActive ? 'Active' : 'Inactive'}`);
        console.log(`Blocked: ${user.isBlocked ? 'Yes' : 'No'}`);
        console.log(`Email Verified: ${user.emailVerification.isVerified ? 'Yes' : 'No'}`);
        
        if (user.address) {
            console.log(`Address: ${user.address}`);
            if (user.city) console.log(`City: ${user.city}, ${user.state} ${user.zipCode}`);
        }

        if (user.userType === 'vendor') {
            console.log(`Vendor Approved: ${user.vendorStatus.isApproved ? 'Yes' : 'Pending'}`);
        }

        console.log(`Created: ${user.createdAt.toLocaleDateString()}`);
        console.log(`Last Active: ${user.lastActiveAt.toLocaleDateString()}`);

        if (user.security.lastLogin) {
            console.log(`Last Login: ${user.security.lastLogin.toLocaleString()}`);
        }

        if (user.statistics.totalOrders > 0) {
            console.log(`\n📊 STATISTICS:`);
            console.log(`Total Orders: ${user.statistics.totalOrders}`);
            console.log(`Total Spent: $${user.statistics.totalSpent.toFixed(2)}`);
            console.log(`Average Order: $${user.statistics.averageOrderValue.toFixed(2)}`);
        }

        console.log('\n✅ Lookup complete');

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await mongoose.connection.close();
    }
}

// Run the lookup
lookupUser(email);