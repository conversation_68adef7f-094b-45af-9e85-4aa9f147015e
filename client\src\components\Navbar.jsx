import React, { useState } from "react";
import { SearchOutlined, UserOutlined, ShoppingCartOutlined, MessageOutlined, DownOutlined, MenuOutlined, LogoutOutlined, SettingOutlined, HeartOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import { message } from "antd";

const Navbar = () => {
    const [isProductDropdownOpen, setIsProductDropdownOpen] = useState(false);
    const [isUserModalOpen, setIsUserModalOpen] = useState(false);
    const [isCartModalOpen, setIsCartModalOpen] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const navigate = useNavigate();
    const { user, isAuthenticated, logout, userType } = useAuth();

    const productCategories = [
        "Electronics",
        "Fashion",
        "Home & Garden",
        "Sports",
        "Books",
        "Toys",
        "Beauty",
        "Automotive"
    ];

    const handleOrdersClick = () => {
        navigate('/orders');
        setIsMobileMenuOpen(false);
    };

    const handleCartClick = () => {
        navigate('/cart');
        setIsMobileMenuOpen(false);
        setIsCartModalOpen(false);
    };

    const handleProfileClick = () => {
        if (isAuthenticated) {
            navigate('/profile');
        } else {
            navigate('/auth');
        }
        setIsUserModalOpen(false);
        setIsMobileMenuOpen(false);
    };

    const handleLogout = async () => {
        try {
            await logout();
            message.success('Logged out successfully');
            navigate('/');
        } catch (error) {
            message.error('Logout failed');
        }
        setIsUserModalOpen(false);
        setIsMobileMenuOpen(false);
    };

    const closeAllModals = () => {
        setIsProductDropdownOpen(false);
        setIsUserModalOpen(false);
        setIsCartModalOpen(false);
        setIsMobileMenuOpen(false);
    };

    const getUserDisplayName = () => {
        if (!user) return 'Guest';
        if (userType === 'vendor') {
            return user.businessName || user.contactPerson || 'Vendor';
        }
        return user.fullName || `${user.firstName} ${user.lastName}` || user.email || 'User';
    };

    const getUserAvatar = () => {
        if (user?.avatar) {
            return user.avatar;
        }
        return null;
    };

    return (
        <>
            <div className="border-b border-gray-800 bg-black shadow-sm">
                <div className="container mx-auto px-2 sm:px-4">
                    {/* Main Navbar */}
                    <div className="flex items-center justify-between h-14 sm:h-16">
                        {/* Logo */}
                        <div className="flex items-center flex-shrink-0">
                            <a href="/" className="flex items-center">
                                <img 
                                    src="https://i.ibb.co/9mQR1Z1H/Alicartify-Logo.png" 
                                    alt="Alicartify Logo"
                                    className="h-8 sm:h-10 w-auto object-contain"
                                />
                            </a>
                        </div>

                        {/* Desktop Search Bar */}
                        <div className="hidden lg:flex flex-1 mx-4 xl:mx-8">
                            <div className="relative w-full max-w-3xl">
                                <div className="flex">
                                    <div className="relative">
                                        <button 
                                            className="flex items-center px-2 xl:px-3 border border-r-0 border-gray-600 rounded-l-md bg-gray-800 text-white h-10 hover:bg-gray-700 text-xs xl:text-sm"
                                            onClick={() => setIsProductDropdownOpen(!isProductDropdownOpen)}
                                        >
                                            <span className="hidden xl:inline">Products</span>
                                            <span className="xl:hidden">All</span>
                                            <DownOutlined className="ml-1 text-xs" />
                                        </button>
                                        
                                        {/* Products Dropdown */}
                                        {isProductDropdownOpen && (
                                            <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                                {productCategories.map((category, index) => (
                                                    <a
                                                        key={index}
                                                        href="#"
                                                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                                        onClick={() => setIsProductDropdownOpen(false)}
                                                    >
                                                        {category}
                                                    </a>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    <input
                                        type="text"
                                        placeholder="What are you looking for..."
                                        className="flex-1 py-2 px-3 xl:px-4 border border-gray-600 focus:outline-none h-10 bg-white text-black text-sm"
                                    />
                                    <button className="flex items-center justify-center px-3 xl:px-4 bg-orange-500 text-white rounded-r-md h-10 hover:bg-orange-600">
                                        <SearchOutlined />
                                        <span className="ml-1 hidden xl:inline text-sm">Search</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Desktop Right Navigation */}
                        <div className="hidden lg:flex items-center space-x-3 xl:space-x-6">
                            {/* Join Free - only show if not authenticated */}
                            {!isAuthenticated && (
                                <button 
                                    onClick={() => navigate('/auth')}
                                    className="hidden xl:block text-sm text-white hover:text-orange-500 whitespace-nowrap"
                                >
                                    Join Free
                                </button>
                            )}

                            {/* Messages */}
                            <a href="#" className="hidden xl:flex items-center text-sm text-white hover:text-orange-500 whitespace-nowrap">
                                <MessageOutlined className="mr-1" />
                                <span>Messages</span>
                            </a>

                            {/* Orders */}
                            <button 
                                onClick={handleOrdersClick}
                                className="text-sm text-white hover:text-orange-500 whitespace-nowrap"
                            >
                                <span>Orders</span>
                            </button>

                            {/* Test Panels - Development only */}
                            <button 
                                onClick={() => navigate('/test-panels')}
                                className="text-sm text-yellow-400 hover:text-yellow-300 whitespace-nowrap"
                            >
                                <span>Test Panels</span>
                            </button>

                            {/* User Icons */}
                            <div className="flex items-center space-x-3 xl:space-x-4">
                                {/* User Icon with Modal */}
                                <div className="relative">
                                    <button 
                                        onClick={() => setIsUserModalOpen(!isUserModalOpen)}
                                        className="flex items-center space-x-2 text-white hover:text-orange-500 text-sm"
                                    >
                                        {getUserAvatar() ? (
                                            <img 
                                                src={getUserAvatar()} 
                                                alt="Profile" 
                                                className="w-8 h-8 rounded-full object-cover border-2 border-gray-600"
                                            />
                                        ) : (
                                            <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                                <UserOutlined className="text-white text-sm" />
                                            </div>
                                        )}
                                        {isAuthenticated && (
                                            <span className="hidden xl:block max-w-24 truncate">
                                                {getUserDisplayName()}
                                            </span>
                                        )}
                                    </button>
                                    
                                    {/* User Modal */}
                                    {isUserModalOpen && (
                                        <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                            {isAuthenticated ? (
                                                <div className="py-2">
                                                    {/* User Info Header */}
                                                    <div className="px-4 py-3 border-b border-gray-200">
                                                        <div className="flex items-center space-x-3">
                                                            {getUserAvatar() ? (
                                                                <img 
                                                                    src={getUserAvatar()} 
                                                                    alt="Profile" 
                                                                    className="w-10 h-10 rounded-full object-cover"
                                                                />
                                                            ) : (
                                                                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                                                    <UserOutlined className="text-gray-500" />
                                                                </div>
                                                            )}
                                                            <div className="flex-1 min-w-0">
                                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                                    {getUserDisplayName()}
                                                                </p>
                                                                <p className="text-xs text-gray-500 truncate">
                                                                    {user.email}
                                                                </p>
                                                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 mt-1">
                                                                    {userType === 'vendor' ? 'Vendor' : 'Customer'}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Menu Items */}
                                                    <button 
                                                        onClick={handleProfileClick}
                                                        className="w-full text-left flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        <UserOutlined className="mr-3" />
                                                        My Profile
                                                    </button>
                                                    <button 
                                                        onClick={() => {
                                                            navigate('/settings');
                                                            setIsUserModalOpen(false);
                                                        }}
                                                        className="w-full text-left flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        <SettingOutlined className="mr-3" />
                                                        Account Settings
                                                    </button>
                                                    <button 
                                                        onClick={() => {
                                                            navigate('/wishlist');
                                                            setIsUserModalOpen(false);
                                                        }}
                                                        className="w-full text-left flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        <HeartOutlined className="mr-3" />
                                                        Wishlist
                                                    </button>
                                                    <hr className="my-1" />
                                                    <button 
                                                        onClick={handleLogout}
                                                        className="w-full text-left flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                                    >
                                                        <LogoutOutlined className="mr-3" />
                                                        Sign Out
                                                    </button>
                                                </div>
                                            ) : (
                                                <div className="py-2">
                                                    <div className="px-4 py-3 border-b border-gray-200">
                                                        <p className="text-sm text-gray-600">Welcome to Alicartify</p>
                                                    </div>
                                                    <button 
                                                        onClick={() => {
                                                            navigate('/auth');
                                                            setIsUserModalOpen(false);
                                                        }}
                                                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        Sign In / Sign Up
                                                    </button>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                                
                                {/* Cart Icon with Modal */}
                                <div className="relative">
                                    <button 
                                        onClick={() => setIsCartModalOpen(!isCartModalOpen)}
                                        className="text-white hover:text-orange-500 text-lg"
                                    >
                                        <ShoppingCartOutlined />
                                    </button>
                                    
                                    {/* Cart Modal */}
                                    {isCartModalOpen && (
                                        <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                            <div className="p-4">
                                                <h3 className="text-lg font-semibold text-gray-800 mb-3">Shopping Cart</h3>
                                                <p className="text-gray-600 text-sm mb-4">Your cart is empty</p>
                                                <button 
                                                    onClick={handleCartClick}
                                                    className="w-full bg-orange-500 text-white py-2 px-4 rounded hover:bg-orange-600"
                                                >
                                                    View Cart
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Mobile Menu Button and Icons */}
                        <div className="lg:hidden flex items-center space-x-2">
                            {/* Mobile User and Cart Icons */}
                            <div className="flex items-center space-x-2">
                                {/* User Icon */}
                                <div className="relative">
                                    <button 
                                        onClick={() => {
                                            setIsUserModalOpen(!isUserModalOpen);
                                            setIsCartModalOpen(false);
                                            setIsMobileMenuOpen(false);
                                        }}
                                        className="flex items-center text-white hover:text-orange-500 p-2 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                                        style={{ minHeight: '44px', minWidth: '44px' }}
                                    >
                                        {getUserAvatar() ? (
                                            <img 
                                                src={getUserAvatar()} 
                                                alt="Profile" 
                                                className="w-6 h-6 rounded-full object-cover border border-gray-600"
                                            />
                                        ) : (
                                            <UserOutlined className="text-lg" />
                                        )}
                                    </button>
                                    
                                    {/* Mobile User Modal */}
                                    {isUserModalOpen && (
                                        <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                            {isAuthenticated ? (
                                                <div className="py-2">
                                                    {/* User Info Header */}
                                                    <div className="px-4 py-3 border-b border-gray-200">
                                                        <div className="flex items-center space-x-3">
                                                            {getUserAvatar() ? (
                                                                <img 
                                                                    src={getUserAvatar()} 
                                                                    alt="Profile" 
                                                                    className="w-10 h-10 rounded-full object-cover"
                                                                />
                                                            ) : (
                                                                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                                                    <UserOutlined className="text-gray-500" />
                                                                </div>
                                                            )}
                                                            <div className="flex-1 min-w-0">
                                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                                    {getUserDisplayName()}
                                                                </p>
                                                                <p className="text-xs text-gray-500 truncate">
                                                                    {user.email}
                                                                </p>
                                                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 mt-1">
                                                                    {userType === 'vendor' ? 'Vendor' : 'Customer'}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Menu Items */}
                                                    <button 
                                                        onClick={handleProfileClick}
                                                        className="w-full text-left flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
                                                    >
                                                        <UserOutlined className="mr-3" />
                                                        My Profile
                                                    </button>
                                                    <button 
                                                        onClick={() => {
                                                            navigate('/settings');
                                                            setIsUserModalOpen(false);
                                                        }}
                                                        className="w-full text-left flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
                                                    >
                                                        <SettingOutlined className="mr-3" />
                                                        Account Settings
                                                    </button>
                                                    <button 
                                                        onClick={() => {
                                                            navigate('/wishlist');
                                                            setIsUserModalOpen(false);
                                                        }}
                                                        className="w-full text-left flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
                                                    >
                                                        <HeartOutlined className="mr-3" />
                                                        Wishlist
                                                    </button>
                                                    <hr className="my-1" />
                                                    <button 
                                                        onClick={handleLogout}
                                                        className="w-full text-left flex items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 active:bg-red-100 touch-manipulation"
                                                    >
                                                        <LogoutOutlined className="mr-3" />
                                                        Sign Out
                                                    </button>
                                                </div>
                                            ) : (
                                                <div className="py-2">
                                                    <div className="px-4 py-3 border-b border-gray-200">
                                                        <p className="text-sm text-gray-600">Welcome to Alicartify</p>
                                                    </div>
                                                    <button 
                                                        onClick={() => {
                                                            navigate('/auth');
                                                            setIsUserModalOpen(false);
                                                        }}
                                                        className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
                                                    >
                                                        Sign In / Sign Up
                                                    </button>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                                
                                {/* Cart Icon */}
                                <div className="relative">
                                    <button 
                                        onClick={() => {
                                            setIsCartModalOpen(!isCartModalOpen);
                                            setIsUserModalOpen(false);
                                            setIsMobileMenuOpen(false);
                                        }}
                                        className="text-white hover:text-orange-500 text-lg p-2 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                                        style={{ minHeight: '44px', minWidth: '44px' }}
                                    >
                                        <ShoppingCartOutlined />
                                    </button>
                                    
                                    {/* Cart Modal */}
                                    {isCartModalOpen && (
                                        <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                            <div className="p-4">
                                                <h3 className="text-lg font-semibold text-gray-800 mb-3">Shopping Cart</h3>
                                                <p className="text-gray-600 text-sm mb-4">Your cart is empty</p>
                                                <button 
                                                    onClick={handleCartClick}
                                                    className="w-full bg-orange-500 text-white py-3 px-4 rounded hover:bg-orange-600 active:bg-orange-700 transition-colors touch-manipulation"
                                                    style={{ minHeight: '44px' }}
                                                >
                                                    View Cart
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Mobile Menu Button */}
                            <button 
                                onClick={() => {
                                    setIsMobileMenuOpen(!isMobileMenuOpen);
                                    setIsUserModalOpen(false);
                                    setIsCartModalOpen(false);
                                }}
                                className="text-white hover:text-orange-500 text-lg p-2 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                                style={{ minHeight: '44px', minWidth: '44px' }}
                            >
                                <MenuOutlined />
                            </button>
                        </div>
                    </div>

                    {/* Mobile Search Bar */}
                    <div className="lg:hidden px-2 pb-3">
                        <div className="relative w-full">
                            <div className="flex">
                                <div className="relative">
                                    <button 
                                        className="flex items-center px-3 border border-r-0 border-gray-600 rounded-l-md bg-gray-800 text-white h-10 hover:bg-gray-700 text-sm active:bg-gray-600 transition-colors touch-manipulation"
                                        onClick={() => setIsProductDropdownOpen(!isProductDropdownOpen)}
                                        style={{ minHeight: '44px' }}
                                    >
                                        All <DownOutlined className="ml-1 text-xs" />
                                    </button>
                                    
                                    {/* Mobile Products Dropdown */}
                                    {isProductDropdownOpen && (
                                        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                            {productCategories.map((category, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 active:bg-gray-200 touch-manipulation"
                                                    onClick={() => setIsProductDropdownOpen(false)}
                                                >
                                                    {category}
                                                </a>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <input
                                    type="text"
                                    placeholder="Search products..."
                                    className="flex-1 py-2 px-3 border border-gray-600 focus:outline-none h-10 bg-white text-black text-sm"
                                    style={{ minHeight: '44px' }}
                                />
                                <button 
                                    className="flex items-center justify-center px-3 bg-orange-500 text-white rounded-r-md h-10 hover:bg-orange-600 active:bg-orange-700 transition-colors touch-manipulation"
                                    style={{ minHeight: '44px' }}
                                >
                                    <SearchOutlined />
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Mobile Menu */}
                    {isMobileMenuOpen && (
                        <div className="lg:hidden border-t border-gray-700 bg-black">
                            <div className="px-2 py-3 space-y-1">
                                {!isAuthenticated && (
                                    <button 
                                        onClick={() => {
                                            navigate('/auth');
                                            setIsMobileMenuOpen(false);
                                        }}
                                        className="block w-full text-left px-3 py-3 text-white hover:text-orange-500 hover:bg-gray-800 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                                        style={{ minHeight: '44px' }}
                                    >
                                        Join Free
                                    </button>
                                )}
                                <a 
                                    href="#" 
                                    className="block px-3 py-3 text-white hover:text-orange-500 hover:bg-gray-800 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                                    style={{ minHeight: '44px' }}
                                >
                                    Messages
                                </a>
                                <button 
                                    onClick={handleOrdersClick}
                                    className="block w-full text-left px-3 py-3 text-white hover:text-orange-500 hover:bg-gray-800 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                                    style={{ minHeight: '44px' }}
                                >
                                    Orders
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            
            {/* Overlay to close dropdowns when clicking outside */}
            {(isProductDropdownOpen || isUserModalOpen || isCartModalOpen || isMobileMenuOpen) && (
                <div 
                    className="fixed inset-0 z-40" 
                    onClick={closeAllModals}
                ></div>
            )}
        </>
    );
};

export default Navbar;