
require('dotenv').config();
const express = require('express');
const http = require('http');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss');

// Import database connection
const connectDB = require('./src/utils/database');

// Import routes
const authRoutes = require('./src/routes/authRoutes');
const adminRoutes = require('./src/routes/admin');

// Import Socket.IO service
const socketService = require('./src/services/socketService');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 8000;

// Connect to database
connectDB();

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
    message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);

// CORS configuration - Allow all localhost origins in development
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);
        
        // In development, allow all localhost origins
        if (process.env.NODE_ENV !== 'production') {
            if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
                return callback(null, true);
            }
        }
        
        // Production allowed origins
        const allowedOrigins = [
            process.env.FRONTEND_URL || 'http://localhost:3000',
            'https://multi-vendor-ecommerce-seven.vercel.app', // Your live frontend
            'https://multi-vendor-server-1tb9.onrender.com', // Your live server
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:5173', // Vite default port
            'http://localhost:4173', // Vite preview port
            'http://127.0.0.1:3000',
            'http://127.0.0.1:5173',
            'http://127.0.0.1:4173'
        ];
        
        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
} else {
    app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Custom middleware to handle text/plain content type that should be JSON
app.use((req, res, next) => {
    const contentType = req.headers['content-type'];
    
    // If content-type is text/plain but the request has a body that looks like JSON
    if (contentType && contentType.includes('text/plain') && req.method !== 'GET') {
        console.log('🔧 Detected text/plain content-type, attempting to parse as JSON...');
        
        let rawBody = '';
        
        req.on('data', chunk => {
            rawBody += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                if (rawBody.trim()) {
                    const parsedBody = JSON.parse(rawBody);
                    req.body = parsedBody;
                    console.log('✅ Successfully parsed text/plain as JSON:', parsedBody);
                } else {
                    req.body = {};
                }
            } catch (error) {
                console.log('❌ Failed to parse text/plain as JSON, keeping as string');
                req.body = rawBody;
            }
            next();
        });
        
        req.on('error', (error) => {
            console.error('Error reading request body:', error);
            req.body = {};
            next();
        });
    } else {
        next();
    }
});



// Data sanitization against NoSQL query injection
// Temporarily disabled due to compatibility issue with Express 5.x
// app.use(mongoSanitize({
//     replaceWith: '_'
// }));

// Custom NoSQL injection protection middleware
app.use((req, res, next) => {
    const sanitize = (obj) => {
        if (obj && typeof obj === 'object') {
            for (const key in obj) {
                if (typeof obj[key] === 'string') {
                    // Remove potential NoSQL injection patterns
                    obj[key] = obj[key].replace(/\$where|\$ne|\$in|\$nin|\$and|\$or|\$nor|\$not|\$exists|\$type|\$mod|\$regex|\$text|\$search/gi, '');
                } else if (typeof obj[key] === 'object') {
                    sanitize(obj[key]);
                }
            }
        }
    };

    if (req.body) sanitize(req.body);
    if (req.query) sanitize(req.query);
    if (req.params) sanitize(req.params);

    next();
});

// Data sanitization against XSS
app.use((req, res, next) => {
    if (req.body) {
        Object.keys(req.body).forEach(key => {
            if (typeof req.body[key] === 'string') {
                req.body[key] = xss(req.body[key]);
            }
        });
    }
    next();
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Server is running successfully',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'Welcome to Multi-Vendor eCommerce API',
        version: '1.0.0',
        documentation: '/api/docs',
        endpoints: {
            auth: '/api/auth',
            health: '/api/health'
        }
    });
});

// 404 handler
app.all('*path', (req, res) => {
    res.status(404).json({
        success: false,
        message: `Route ${req.originalUrl} not found`
    });
});

// Global error handler
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);
    
    // Mongoose validation error
    if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => err.message);
        return res.status(400).json({
            success: false,
            message: 'Validation Error',
            errors
        });
    }
    
    // Mongoose duplicate key error
    if (error.code === 11000) {
        const field = Object.keys(error.keyValue)[0];
        return res.status(400).json({
            success: false,
            message: `${field} already exists`
        });
    }
    
    // JWT errors
    if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
            success: false,
            message: 'Invalid token'
        });
    }
    
    if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
            success: false,
            message: 'Token expired'
        });
    }
    
    // CORS error
    if (error.message === 'Not allowed by CORS') {
        return res.status(403).json({
            success: false,
            message: 'CORS policy violation'
        });
    }
    
    // Default error
    res.status(error.status || 500).json({
        success: false,
        message: error.message || 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    process.exit(0);
});

// Initialize Socket.IO
socketService.initialize(server);

server.listen(PORT, () => {
    console.log(`🚀 Server is running on port ${PORT}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
    console.log(`🔌 Socket.IO enabled for real-time features`);
    console.log(`\n💡 Local Development URLs:`);
    console.log(`   Frontend: http://localhost:5173`);
    console.log(`   Backend:  http://localhost:${PORT}`);
    console.log(`   API:      http://localhost:${PORT}/api`);
});

module.exports = app;