import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Upload,
  Avatar,
  Space,
  Tabs
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  UploadOutlined,
  DollarOutlined,
  MailOutlined,
  SecurityScanOutlined,
  GlobalOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Settings = () => {
  const [generalForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSaveSettings = async (formType, values) => {
    setLoading(true);
    try {
      // Simulate API call
      setTimeout(() => {
        message.success(`${formType} settings saved successfully`);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('Failed to save settings');
      setLoading(false);
    }
  };

  const generalSettings = (
    <Card>
      <Form
        form={generalForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('General', values)}
        initialValues={{
          siteName: 'Multi-Vendor eCommerce',
          siteDescription: 'Your ultimate shopping destination',
          currency: 'USD',
          timezone: 'UTC',
          maintenanceMode: false,
          userRegistration: true,
          vendorRegistration: true,
          guestCheckout: true,
          productReviews: true,
          wishlist: true
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="siteName"
              label="Site Name"
              rules={[{ required: true, message: 'Please enter site name' }]}
            >
              <Input placeholder="Enter site name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="currency"
              label="Default Currency"
              rules={[{ required: true, message: 'Please select currency' }]}
            >
              <Select placeholder="Select currency">
                <Option value="USD">USD - US Dollar</Option>
                <Option value="EUR">EUR - Euro</Option>
                <Option value="GBP">GBP - British Pound</Option>
                <Option value="JPY">JPY - Japanese Yen</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="siteDescription"
          label="Site Description"
        >
          <TextArea rows={3} placeholder="Enter site description" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="timezone"
              label="Timezone"
            >
              <Select placeholder="Select timezone">
                <Option value="UTC">UTC</Option>
                <Option value="EST">EST - Eastern Time</Option>
                <Option value="PST">PST - Pacific Time</Option>
                <Option value="GMT">GMT - Greenwich Mean Time</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="language"
              label="Default Language"
            >
              <Select placeholder="Select language">
                <Option value="en">English</Option>
                <Option value="es">Spanish</Option>
                <Option value="fr">French</Option>
                <Option value="de">German</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Features</Divider>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="maintenanceMode" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Maintenance Mode</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="userRegistration" valuePropName="checked">
              <Space>
                <Switch />
                <Text>User Registration</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="vendorRegistration" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Vendor Registration</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="guestCheckout" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Guest Checkout</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="productReviews" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Product Reviews</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="wishlist" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Wishlist Feature</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save General Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const paymentSettings = (
    <Card>
      <Form
        form={paymentForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Payment', values)}
        initialValues={{
          defaultCommission: 15,
          paymentMethods: ['stripe', 'paypal'],
          autoPayouts: true,
          minimumPayout: 100,
          payoutSchedule: 'weekly'
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="defaultCommission"
              label="Default Commission Rate (%)"
              rules={[{ required: true, message: 'Please enter commission rate' }]}
            >
              <InputNumber
                min={0}
                max={100}
                style={{ width: '100%' }}
                placeholder="Enter commission rate"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="minimumPayout"
              label="Minimum Payout Amount ($)"
              rules={[{ required: true, message: 'Please enter minimum payout' }]}
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="Enter minimum payout amount"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="paymentMethods"
              label="Enabled Payment Methods"
            >
              <Select mode="multiple" placeholder="Select payment methods">
                <Option value="stripe">Stripe</Option>
                <Option value="paypal">PayPal</Option>
                <Option value="square">Square</Option>
                <Option value="razorpay">Razorpay</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="payoutSchedule"
              label="Payout Schedule"
            >
              <Select placeholder="Select payout schedule">
                <Option value="daily">Daily</Option>
                <Option value="weekly">Weekly</Option>
                <Option value="monthly">Monthly</Option>
                <Option value="manual">Manual</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="autoPayouts" valuePropName="checked">
          <Space>
            <Switch />
            <Text>Enable Automatic Payouts</Text>
          </Space>
        </Form.Item>

        <Divider>Payment Gateway Configuration</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="stripePublicKey" label="Stripe Public Key">
              <Input placeholder="Enter Stripe public key" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="stripeSecretKey" label="Stripe Secret Key">
              <Input.Password placeholder="Enter Stripe secret key" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="paypalClientId" label="PayPal Client ID">
              <Input placeholder="Enter PayPal client ID" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="paypalClientSecret" label="PayPal Client Secret">
              <Input.Password placeholder="Enter PayPal client secret" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Payment Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const emailSettings = (
    <Card>
      <Form
        form={emailForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Email', values)}
        initialValues={{
          smtpHost: 'smtp.gmail.com',
          smtpPort: 587,
          smtpSecurity: 'tls',
          fromName: 'Multi-Vendor eCommerce',
          orderNotifications: true,
          userNotifications: true,
          vendorNotifications: true,
          marketingEmails: false
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="smtpHost"
              label="SMTP Host"
              rules={[{ required: true, message: 'Please enter SMTP host' }]}
            >
              <Input placeholder="Enter SMTP host" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="smtpPort"
              label="SMTP Port"
              rules={[{ required: true, message: 'Please enter SMTP port' }]}
            >
              <InputNumber style={{ width: '100%' }} placeholder="Enter SMTP port" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="smtpUsername"
              label="SMTP Username"
              rules={[{ required: true, message: 'Please enter SMTP username' }]}
            >
              <Input placeholder="Enter SMTP username" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="smtpPassword"
              label="SMTP Password"
              rules={[{ required: true, message: 'Please enter SMTP password' }]}
            >
              <Input.Password placeholder="Enter SMTP password" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="smtpSecurity" label="Security">
              <Select placeholder="Select security type">
                <Option value="none">None</Option>
                <Option value="ssl">SSL</Option>
                <Option value="tls">TLS</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="fromName" label="From Name">
              <Input placeholder="Enter sender name" />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Email Notifications</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="orderNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Order Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="userNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>User Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="vendorNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Vendor Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="marketingEmails" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Marketing Emails</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Email Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const securitySettings = (
    <Card>
      <Form
        form={securityForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Security', values)}
        initialValues={{
          twoFactorAuth: false,
          sessionTimeout: 30,
          passwordMinLength: 8,
          requireSpecialChars: true,
          maxLoginAttempts: 5,
          accountLockoutTime: 15,
          enableCaptcha: true
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="sessionTimeout"
              label="Session Timeout (minutes)"
            >
              <InputNumber
                min={5}
                max={1440}
                style={{ width: '100%' }}
                placeholder="Enter session timeout"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="passwordMinLength"
              label="Minimum Password Length"
            >
              <InputNumber
                min={6}
                max={50}
                style={{ width: '100%' }}
                placeholder="Enter minimum password length"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="maxLoginAttempts"
              label="Max Login Attempts"
            >
              <InputNumber
                min={3}
                max={10}
                style={{ width: '100%' }}
                placeholder="Enter max login attempts"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="accountLockoutTime"
              label="Account Lockout Time (minutes)"
            >
              <InputNumber
                min={5}
                max={1440}
                style={{ width: '100%' }}
                placeholder="Enter lockout time"
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Security Features</Divider>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="twoFactorAuth" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Two-Factor Authentication</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="requireSpecialChars" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Require Special Characters</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="enableCaptcha" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Enable CAPTCHA</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Security Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const tabItems = [
    {
      key: 'general',
      label: (
        <span>
          <GlobalOutlined />
          General
        </span>
      ),
      children: generalSettings,
    },
    {
      key: 'payment',
      label: (
        <span>
          <DollarOutlined />
          Payment
        </span>
      ),
      children: paymentSettings,
    },
    {
      key: 'email',
      label: (
        <span>
          <MailOutlined />
          Email
        </span>
      ),
      children: emailSettings,
    },
    {
      key: 'security',
      label: (
        <span>
          <SecurityScanOutlined />
          Security
        </span>
      ),
      children: securitySettings,
    },
  ];

  return (
    <div>
      <Title level={2}>
        <SettingOutlined /> System Settings
      </Title>
      <Tabs defaultActiveKey="general" items={tabItems} />
    </div>
  );
};

export default Settings;