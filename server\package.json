{"name": "alicartify-server", "version": "1.0.0", "description": "Alicartify - Advanced multi-vendor eCommerce platform server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "test:auth": "node test-auth.js", "test:db": "node test-db-connection.js", "setup:mongodb": "node setup-mongodb.js", "generate-secrets": "node generate-secrets.js"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.0.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.3", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^6.9.8", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10", "jest": "^29.7.0", "supertest": "^6.3.4", "axios": "^1.6.2"}, "keywords": ["ecommerce", "multi-vendor", "marketplace", "nodejs", "express", "mongodb"], "author": "Your Name", "license": "MIT"}