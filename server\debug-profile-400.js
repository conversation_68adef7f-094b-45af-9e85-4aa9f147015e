/**
 * Debug script to identify the specific 400 error in profile update
 * Run with: node debug-profile-400.js
 */

const axios = require('axios');

// Use localhost for local testing
const BASE_URL = process.env.SERVER_URL || 'http://localhost:5000/api';

async function debugProfileUpdate() {
    console.log('🔍 Debugging Profile Update 400 Error...\n');

    try {
        // Step 1: Check server health
        console.log('1. Checking server health...');
        await axios.get(`${BASE_URL}/auth/health`);
        console.log('   ✅ Server is running\n');

        // Step 2: Create a test user and get token
        console.log('2. Creating test user...');
        const timestamp = Date.now();
        const testUser = {
            firstName: 'Debug',
            lastName: 'User',
            email: `debug.${timestamp}@example.com`,
            password: 'DebugPassword123',
            userType: 'user'
        };

        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
        const token = registerResponse.data.data.token;
        console.log('   ✅ Test user created and token obtained\n');

        // Step 3: Test various profile update scenarios
        const testCases = [
            {
                name: 'Empty object',
                data: {}
            },
            {
                name: 'Null data',
                data: null
            },
            {
                name: 'Valid single field',
                data: { firstName: 'Updated' }
            },
            {
                name: 'Multiple valid fields',
                data: { 
                    firstName: 'John',
                    lastName: 'Doe',
                    city: 'New York'
                }
            },
            {
                name: 'Invalid field',
                data: { invalidField: 'test' }
            },
            {
                name: 'Mixed valid and invalid',
                data: { 
                    firstName: 'Valid',
                    invalidField: 'invalid'
                }
            },
            {
                name: 'Empty string values',
                data: { 
                    firstName: '',
                    lastName: 'Doe'
                }
            },
            {
                name: 'Preferences update',
                data: {
                    preferences: {
                        language: 'en',
                        currency: 'USD'
                    }
                }
            },
            {
                name: 'Invalid preference value',
                data: {
                    preferences: {
                        language: 'invalid_lang'
                    }
                }
            }
        ];

        for (let i = 0; i < testCases.length; i++) {
            const testCase = testCases[i];
            console.log(`${i + 3}. Testing: ${testCase.name}`);
            
            try {
                const response = await axios.put(`${BASE_URL}/auth/profile`, testCase.data, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log(`   ✅ Success: ${response.status} - ${response.data.message}`);
                if (response.data.data?.updatedFields) {
                    console.log(`   📝 Updated fields: ${response.data.data.updatedFields.join(', ')}`);
                }
                
            } catch (error) {
                if (error.response) {
                    console.log(`   ❌ Error: ${error.response.status} - ${error.response.data.message}`);
                    
                    if (error.response.data.allowedFields) {
                        console.log(`   📋 Allowed fields: ${error.response.data.allowedFields.join(', ')}`);
                    }
                    if (error.response.data.receivedFields) {
                        console.log(`   📥 Received fields: ${error.response.data.receivedFields.join(', ')}`);
                    }
                    if (error.response.data.errors) {
                        console.log(`   📝 Validation errors:`);
                        error.response.data.errors.forEach(err => {
                            console.log(`      - ${err.field}: ${err.message}`);
                        });
                    }
                } else {
                    console.log(`   ❌ Network error: ${error.message}`);
                }
            }
            
            console.log(''); // Empty line for readability
        }

        // Step 4: Test authentication issues
        console.log(`${testCases.length + 3}. Testing authentication issues...\n`);
        
        // No token
        console.log('   a) No authorization header:');
        try {
            await axios.put(`${BASE_URL}/auth/profile`, { firstName: 'Test' });
            console.log('      ❌ Should have failed');
        } catch (error) {
            console.log(`      ✅ Correctly rejected: ${error.response.status} - ${error.response.data.message}`);
        }

        // Invalid token
        console.log('\n   b) Invalid token:');
        try {
            await axios.put(`${BASE_URL}/auth/profile`, { firstName: 'Test' }, {
                headers: { 'Authorization': 'Bearer invalid_token' }
            });
            console.log('      ❌ Should have failed');
        } catch (error) {
            console.log(`      ✅ Correctly rejected: ${error.response.status} - ${error.response.data.message}`);
        }

        // Malformed header
        console.log('\n   c) Malformed authorization header:');
        try {
            await axios.put(`${BASE_URL}/auth/profile`, { firstName: 'Test' }, {
                headers: { 'Authorization': 'InvalidFormat token123' }
            });
            console.log('      ❌ Should have failed');
        } catch (error) {
            console.log(`      ✅ Correctly rejected: ${error.response.status} - ${error.response.data.message}`);
        }

        console.log('\n🎉 Debug completed! Check the results above to identify the 400 error cause.');
        
    } catch (error) {
        console.error('❌ Debug script error:', error.response?.data || error.message);
    }
}

async function quickTest() {
    console.log('⚡ Quick Profile Update Test\n');
    
    try {
        // Try to login with existing test user
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
            email: '<EMAIL>',
            password: 'TestPassword123'
        });
        
        const token = loginResponse.data.data.token;
        console.log('✅ Logged in with existing test user\n');
        
        // Try a simple profile update
        console.log('Testing simple profile update...');
        const response = await axios.put(`${BASE_URL}/auth/profile`, {
            firstName: 'Updated Name'
        }, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log(`✅ Success: ${response.status} - ${response.data.message}`);
        console.log(`📝 Updated fields: ${response.data.data.updatedFields.join(', ')}`);
        
    } catch (error) {
        if (error.response) {
            console.log(`❌ Error: ${error.response.status} - ${error.response.data.message}`);
            if (error.response.data.errors) {
                console.log('📝 Validation errors:');
                error.response.data.errors.forEach(err => {
                    console.log(`   - ${err.field}: ${err.message}`);
                });
            }
        } else {
            console.log(`❌ Network error: ${error.message}`);
        }
    }
}

// Main execution
const args = process.argv.slice(2);
if (args.includes('--quick')) {
    quickTest();
} else {
    debugProfileUpdate();
}

console.log('\n💡 Usage:');
console.log('   node debug-profile-400.js          # Full debug test');
console.log('   node debug-profile-400.js --quick  # Quick test with existing user');