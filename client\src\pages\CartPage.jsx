import React from "react";
import Header from "../components/Header";
import Footer from "../components/Footer";

const CartPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <div className="text-center">
            <img 
              src="https://i.ibb.co/9mQR1Z1H/Alicartify-Logo.png" 
              alt="Logo" 
              className="w-32 h-32 mx-auto mb-6 object-contain"
            />
            <h1 className="text-2xl font-semibold text-gray-800 mb-4">Shopping Cart</h1>
            <p className="text-gray-600 text-lg">Currently there is no item in cart</p>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default CartPage;