const axios = require('axios');

async function testCategoriesAPI() {
  try {
    console.log('🧪 Testing Categories API...');
    
    // Test local endpoint
    const localUrl = 'http://localhost:5000/api/public/categories';
    
    console.log(`📡 Making request to: ${localUrl}`);
    
    const response = await axios.get(localUrl);
    
    console.log('✅ API Response Status:', response.status);
    console.log('✅ API Response Success:', response.data.success);
    console.log('✅ Categories Count:', response.data.data.categories.length);
    
    console.log('\n📋 Categories List:');
    response.data.data.categories.forEach((category, index) => {
      console.log(`${index + 1}. ${category.name} (ID: ${category._id})`);
    });
    
    console.log('\n🎉 Categories API is working correctly!');
    
  } catch (error) {
    console.error('❌ Error testing categories API:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testCategoriesAPI();