import React, { useState, useEffect } from 'react';
import {
  DashboardOutlined,
  UserOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  BarChartOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons';
import {
  Layout,
  Menu,
  theme,
  Button,
  Avatar,
  Dropdown,
  Badge,
  Space,
  Typography,
  Drawer
} from 'antd';
import { useAuth } from '../../hooks/useAuth';

const { Header, Content, Footer, Sider } = Layout;
const { Text } = Typography;

// Menu items configuration
const menuItems = [
  { key: 'dashboard', icon: <DashboardOutlined />, label: 'Dashboard' },
  { key: 'users', icon: <UserOutlined />, label: 'Users' },
  { key: 'vendors', icon: <ShopOutlined />, label: 'Vendors' },
  { key: 'orders', icon: <ShoppingCartOutlined />, label: 'Orders' },
  { key: 'analytics', icon: <BarChartOutlined />, label: 'Analytics' },
  { key: 'settings', icon: <SettingOutlined />, label: 'Settings' },
];

const AdminLayout = ({ children, activeKey = 'dashboard', onMenuSelect }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { user, logout } = useAuth();
  const { token: { colorBgContainer, borderRadiusLG } } = theme.useToken();

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile) {
        setCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = () => {
    logout();
    window.location.href = '/auth';
  };

  const handleMenuClick = ({ key }) => {
    if (onMenuSelect) onMenuSelect(key);
    if (isMobile) setMobileDrawerVisible(false);
  };

  const userMenuItems = [
    { key: 'profile', icon: <UserOutlined />, label: 'Profile' },
    { key: 'settings', icon: <SettingOutlined />, label: 'Settings' },
    { type: 'divider' },
    { key: 'logout', icon: <LogoutOutlined />, label: 'Logout', onClick: handleLogout },
  ];

  const renderMenu = () => (
    <Menu
      theme="dark"
      selectedKeys={[activeKey]}
      mode="inline"
      items={menuItems}
      onClick={handleMenuClick}
      style={{ border: 'none' }}
    />
  );

  const renderLogo = () => (
    <div style={{
      height: 64,
      margin: 16,
      background: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 6,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontWeight: 'bold',
      fontSize: collapsed && !isMobile ? '14px' : '16px'
    }}>
      {collapsed && !isMobile ? 'AP' : 'Admin Panel'}
    </div>
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Desktop Sidebar */}
      {!isMobile && (
        <Sider 
          collapsible 
          collapsed={collapsed} 
          onCollapse={setCollapsed}
          breakpoint="lg"
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 100,
          }}
        >
          {renderLogo()}
          {renderMenu()}
        </Sider>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Drawer
          title="Admin Panel"
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0 }}
          width={250}
        >
          {renderMenu()}
        </Drawer>
      )}
      
      <Layout style={{ 
        marginLeft: isMobile ? 0 : (collapsed ? 80 : 200), 
        transition: 'margin-left 0.2s' 
      }}>
        <Header
          style={{
            padding: isMobile ? '0 16px' : '0 24px',
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 4px rgba(0,21,41,.08)',
            position: 'sticky',
            top: 0,
            zIndex: 99,
          }}
        >
          <Button
            type="text"
            icon={isMobile ? <MenuFoldOutlined /> : (collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />)}
            onClick={() => isMobile ? setMobileDrawerVisible(true) : setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space size={isMobile ? "small" : "large"}>
            <Badge count={5} size="small">
              <BellOutlined style={{ fontSize: '18px', cursor: 'pointer' }} />
            </Badge>
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} size={isMobile ? "small" : "default"} />
                {!isMobile && <Text strong>{user?.name || 'Admin'}</Text>}
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{ 
          margin: isMobile ? '16px' : '24px', 
          overflow: 'initial' 
        }}>
          <div
            style={{
              padding: isMobile ? 16 : 24,
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            {children}
          </div>
        </Content>
        
        <Footer style={{ 
          textAlign: 'center', 
          background: colorBgContainer,
          padding: isMobile ? '12px' : '24px'
        }}>
          {isMobile ? '©2024 Admin Panel' : 'Multi-Vendor eCommerce ©2024 Admin Panel'}
        </Footer>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;