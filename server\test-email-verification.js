/**
 * Test script to demonstrate email verification behavior
 * Run with: node test-email-verification.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test the verification endpoint with different scenarios
async function testVerificationScenarios() {
    console.log('🔍 Testing Email Verification Scenarios...\n');

    // Example token and email (replace with actual values from your logs)
    const testToken = '883e15f60252304de97d13bb135009c2f7a0ad9445c083da5b848ced36d2323e';
    const testEmail = '<EMAIL>';

    const scenarios = [
        {
            name: 'First Verification Attempt',
            description: 'Should succeed if token is valid and email not yet verified'
        },
        {
            name: 'Second Verification Attempt (Same Token)',
            description: 'Should return "already verified" message with 200 status'
        },
        {
            name: 'Invalid Token',
            description: 'Should return 400 with clear error message',
            token: 'invalid_token_12345',
            email: testEmail
        },
        {
            name: 'Non-existent Email',
            description: 'Should return 400 with user not found message',
            token: testToken,
            email: '<EMAIL>'
        }
    ];

    for (let i = 0; i < scenarios.length; i++) {
        const scenario = scenarios[i];
        console.log(`\n${i + 1}. ${scenario.name}`);
        console.log(`   ${scenario.description}`);
        
        const token = scenario.token || testToken;
        const email = scenario.email || testEmail;
        
        try {
            const response = await axios.get(`${BASE_URL}/auth/verify-email`, {
                params: { token, email }
            });
            
            console.log(`   ✅ Status: ${response.status}`);
            console.log(`   📝 Message: ${response.data.message}`);
            
            if (response.data.alreadyVerified) {
                console.log(`   ℹ️  Email was already verified`);
            }
            if (response.data.verifiedAt) {
                console.log(`   📅 Verified at: ${response.data.verifiedAt}`);
            }
            
        } catch (error) {
            if (error.response) {
                console.log(`   ❌ Status: ${error.response.status}`);
                console.log(`   📝 Message: ${error.response.data.message}`);
                
                if (error.response.data.expired) {
                    console.log(`   ⏰ Token has expired`);
                }
                if (error.response.data.invalid) {
                    console.log(`   🚫 Token is invalid or already used`);
                }
            } else {
                console.log(`   ❌ Network Error: ${error.message}`);
            }
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}

async function demonstrateWorkflow() {
    console.log('\n\n📋 Email Verification Workflow Explanation:\n');
    
    console.log('1️⃣  User registers → Verification email sent with unique token');
    console.log('2️⃣  User clicks email link → First verification request (SUCCESS)');
    console.log('3️⃣  User clicks link again → Second request (ALREADY VERIFIED)');
    console.log('4️⃣  Token is deleted after first use for security');
    console.log('5️⃣  Subsequent requests return friendly "already verified" message\n');
    
    console.log('🔒 Security Features:');
    console.log('   • Single-use tokens prevent replay attacks');
    console.log('   • Tokens expire after 24 hours');
    console.log('   • Clear error messages for different scenarios');
    console.log('   • Graceful handling of already-verified emails\n');
    
    console.log('✅ Expected Behavior:');
    console.log('   • First request: 200 "Email verified successfully"');
    console.log('   • Second request: 200 "Email is already verified"');
    console.log('   • Invalid token: 400 "Invalid verification token"');
    console.log('   • Expired token: 400 "Token has expired"');
}

async function checkServerHealth() {
    try {
        await axios.get(`${BASE_URL}/auth/health`);
        return true;
    } catch (error) {
        console.log('❌ Server is not running. Please start with: npm run dev');
        return false;
    }
}

async function main() {
    console.log('🧪 Email Verification Test Suite\n');
    
    const serverRunning = await checkServerHealth();
    if (!serverRunning) return;
    
    await demonstrateWorkflow();
    await testVerificationScenarios();
    
    console.log('\n\n💡 Key Takeaways:');
    console.log('   • The 400 error on second request is EXPECTED behavior');
    console.log('   • This prevents security vulnerabilities');
    console.log('   • Users should only click verification links once');
    console.log('   • The improved endpoint now gives clearer feedback');
    
    console.log('\n🔧 If you need to test verification:');
    console.log('   1. Register a new user with unique email');
    console.log('   2. Check email for verification link');
    console.log('   3. Click link once (should succeed)');
    console.log('   4. Click again (should show "already verified")');
}

main().catch(console.error);