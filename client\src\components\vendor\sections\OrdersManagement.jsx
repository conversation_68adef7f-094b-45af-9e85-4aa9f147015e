import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Input,
  Descriptions,
  message,
  Steps
} from 'antd';
import {
  ShoppingCartOutlined,
  EyeOutlined,
  SearchOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TruckOutlined,
  CloseCircleOutlined,
  EditOutlined
} from '@ant-design/icons';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Step } = Steps;

const OrdersManagement = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Sample data - replace with actual API calls
  const sampleOrders = [
    {
      id: 'ORD-001',
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      products: [
        { name: 'iPhone 15 Pro', quantity: 1, price: 999.99, sku: 'IPH15PRO001' },
        { name: 'AirPods Pro', quantity: 1, price: 249.99, sku: 'AIRPODS001' }
      ],
      totalAmount: 1249.98,
      commission: 187.50,
      netAmount: 1062.48,
      status: 'delivered',
      orderDate: '2024-03-15',
      shippedDate: '2024-03-16',
      deliveryDate: '2024-03-18',
      shippingAddress: '123 Main St, New York, NY 10001',
      paymentMethod: 'Credit Card',
      trackingNumber: 'TRK123456789',
      customerPhone: '******-0123'
    },
    {
      id: 'ORD-002',
      customerName: 'Jane Smith',
      customerEmail: '<EMAIL>',
      products: [
        { name: 'MacBook Air', quantity: 1, price: 1299.99, sku: 'MBA001' }
      ],
      totalAmount: 1299.99,
      commission: 195.00,
      netAmount: 1104.99,
      status: 'shipped',
      orderDate: '2024-03-20',
      shippedDate: '2024-03-21',
      deliveryDate: null,
      shippingAddress: '456 Oak Ave, Los Angeles, CA 90210',
      paymentMethod: 'PayPal',
      trackingNumber: 'TRK987654321',
      customerPhone: '******-0124'
    },
    {
      id: 'ORD-003',
      customerName: 'Bob Wilson',
      customerEmail: '<EMAIL>',
      products: [
        { name: 'iPad Pro', quantity: 1, price: 799.99, sku: 'IPADPRO001' }
      ],
      totalAmount: 799.99,
      commission: 120.00,
      netAmount: 679.99,
      status: 'processing',
      orderDate: '2024-03-22',
      shippedDate: null,
      deliveryDate: null,
      shippingAddress: '789 Pine St, Chicago, IL 60601',
      paymentMethod: 'Credit Card',
      trackingNumber: null,
      customerPhone: '******-0125'
    },
    {
      id: 'ORD-004',
      customerName: 'Alice Brown',
      customerEmail: '<EMAIL>',
      products: [
        { name: 'AirPods Pro', quantity: 2, price: 249.99, sku: 'AIRPODS001' }
      ],
      totalAmount: 499.98,
      commission: 75.00,
      netAmount: 424.98,
      status: 'pending',
      orderDate: '2024-03-25',
      shippedDate: null,
      deliveryDate: null,
      shippingAddress: '321 Elm St, Miami, FL 33101',
      paymentMethod: 'Credit Card',
      trackingNumber: null,
      customerPhone: '******-0126'
    }
  ];

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      // Simulate API call
      setTimeout(() => {
        setOrders(sampleOrders);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('Failed to fetch orders');
      setLoading(false);
    }
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setDetailModalVisible(true);
  };

  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      const updateData = { status: newStatus };
      
      if (newStatus === 'shipped') {
        updateData.shippedDate = new Date().toISOString().split('T')[0];
        updateData.trackingNumber = `TRK${Date.now()}`;
      } else if (newStatus === 'delivered') {
        updateData.deliveryDate = new Date().toISOString().split('T')[0];
      }

      setOrders(orders.map(order =>
        order.id === orderId
          ? { ...order, ...updateData }
          : order
      ));
      message.success(`Order status updated to ${newStatus}`);
    } catch (error) {
      message.error('Failed to update order status');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'processing':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockCircleOutlined />;
      case 'processing':
        return <ClockCircleOutlined />;
      case 'shipped':
        return <TruckOutlined />;
      case 'delivered':
        return <CheckCircleOutlined />;
      case 'cancelled':
        return <CloseCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getOrderSteps = (order) => {
    const steps = [
      { title: 'Order Placed', status: 'finish' },
      { title: 'Processing', status: order.status === 'pending' ? 'wait' : 'finish' },
      { title: 'Shipped', status: ['shipped', 'delivered'].includes(order.status) ? 'finish' : 'wait' },
      { title: 'Delivered', status: order.status === 'delivered' ? 'finish' : 'wait' }
    ];
    return steps;
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = 
      order.id.toLowerCase().includes(searchText.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      render: (id) => <strong>{id}</strong>,
    },
    {
      title: 'Customer',
      dataIndex: 'customerName',
      key: 'customerName',
      render: (name, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{name}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{record.customerEmail}</div>
        </div>
      ),
    },
    {
      title: 'Products',
      dataIndex: 'products',
      key: 'products',
      render: (products) => (
        <div>
          {products.map((product, index) => (
            <div key={index} style={{ fontSize: '12px' }}>
              {product.name} (x{product.quantity})
            </div>
          ))}
        </div>
      ),
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => `$${amount.toFixed(2)}`,
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: 'Your Earnings',
      dataIndex: 'netAmount',
      key: 'netAmount',
      render: (amount) => (
        <span style={{ color: '#52c41a', fontWeight: 500 }}>
          ${amount.toFixed(2)}
        </span>
      ),
      sorter: (a, b) => a.netAmount - b.netAmount,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Order Date',
      dataIndex: 'orderDate',
      key: 'orderDate',
      sorter: (a, b) => new Date(a.orderDate) - new Date(b.orderDate),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewOrder(record)}
          >
            View Details
          </Button>
          {record.status !== 'delivered' && record.status !== 'cancelled' && (
            <Select
              size="small"
              value={record.status}
              onChange={(value) => handleUpdateOrderStatus(record.id, value)}
              style={{ width: 120 }}
            >
              <Option value="pending">Pending</Option>
              <Option value="processing">Processing</Option>
              <Option value="shipped">Shipped</Option>
              <Option value="delivered">Delivered</Option>
            </Select>
          )}
        </Space>
      ),
    },
  ];

  const totalOrders = orders.length;
  const pendingOrders = orders.filter(order => order.status === 'pending').length;
  const deliveredOrders = orders.filter(order => order.status === 'delivered').length;
  const totalEarnings = orders
    .filter(order => order.status === 'delivered')
    .reduce((sum, order) => sum + order.netAmount, 0);

  return (
    <div>
      <Title level={2}>Orders Management</Title>
      
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Orders"
              value={totalOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Pending Orders"
              value={pendingOrders}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Delivered Orders"
              value={deliveredOrders}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Earnings"
              value={totalEarnings}
              prefix={<DollarOutlined />}
              formatter={(value) => `$${value.toFixed(2)}`}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 16 }}>
          <Space>
            <Input
              placeholder="Search orders..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 150 }}
            >
              <Option value="all">All Status</Option>
              <Option value="pending">Pending</Option>
              <Option value="processing">Processing</Option>
              <Option value="shipped">Shipped</Option>
              <Option value="delivered">Delivered</Option>
            </Select>
          </Space>
          <RangePicker />
        </div>

        <Table
          columns={columns}
          dataSource={filteredOrders}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} orders`,
          }}
        />
      </Card>

      {/* Order Detail Modal */}
      <Modal
        title={`Order Details - ${selectedOrder?.id}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={900}
      >
        {selectedOrder && (
          <div>
            {/* Order Progress */}
            <Card title="Order Progress" style={{ marginBottom: 16 }}>
              <Steps current={getOrderSteps(selectedOrder).findIndex(step => step.status === 'wait')}>
                {getOrderSteps(selectedOrder).map((step, index) => (
                  <Step key={index} title={step.title} />
                ))}
              </Steps>
            </Card>

            {/* Order Information */}
            <Descriptions bordered column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="Order ID">{selectedOrder.id}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedOrder.status)} icon={getStatusIcon(selectedOrder.status)}>
                  {selectedOrder.status.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Customer">{selectedOrder.customerName}</Descriptions.Item>
              <Descriptions.Item label="Email">{selectedOrder.customerEmail}</Descriptions.Item>
              <Descriptions.Item label="Phone">{selectedOrder.customerPhone}</Descriptions.Item>
              <Descriptions.Item label="Payment Method">{selectedOrder.paymentMethod}</Descriptions.Item>
              <Descriptions.Item label="Order Date">{selectedOrder.orderDate}</Descriptions.Item>
              <Descriptions.Item label="Shipped Date">{selectedOrder.shippedDate || 'Not shipped'}</Descriptions.Item>
              <Descriptions.Item label="Delivery Date">{selectedOrder.deliveryDate || 'Not delivered'}</Descriptions.Item>
              <Descriptions.Item label="Tracking Number">
                {selectedOrder.trackingNumber || 'Not assigned'}
              </Descriptions.Item>
              <Descriptions.Item label="Shipping Address" span={2}>
                {selectedOrder.shippingAddress}
              </Descriptions.Item>
            </Descriptions>

            {/* Products */}
            <Title level={4} style={{ marginTop: 24, marginBottom: 16 }}>Products</Title>
            <Table
              dataSource={selectedOrder.products}
              pagination={false}
              size="small"
              columns={[
                {
                  title: 'Product',
                  dataIndex: 'name',
                  key: 'name',
                },
                {
                  title: 'SKU',
                  dataIndex: 'sku',
                  key: 'sku',
                },
                {
                  title: 'Quantity',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  align: 'center',
                },
                {
                  title: 'Price',
                  dataIndex: 'price',
                  key: 'price',
                  render: (price) => `$${price.toFixed(2)}`,
                },
                {
                  title: 'Total',
                  key: 'total',
                  render: (_, record) => `$${(record.quantity * record.price).toFixed(2)}`,
                },
              ]}
              summary={() => (
                <Table.Summary>
                  <Table.Summary.Row>
                    <Table.Summary.Cell colSpan={4}><strong>Subtotal</strong></Table.Summary.Cell>
                    <Table.Summary.Cell>
                      <strong>${selectedOrder.totalAmount.toFixed(2)}</strong>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell colSpan={4}><strong>Commission ({((selectedOrder.commission / selectedOrder.totalAmount) * 100).toFixed(1)}%)</strong></Table.Summary.Cell>
                    <Table.Summary.Cell>
                      <strong style={{ color: '#f5222d' }}>-${selectedOrder.commission.toFixed(2)}</strong>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell colSpan={4}><strong>Your Earnings</strong></Table.Summary.Cell>
                    <Table.Summary.Cell>
                      <strong style={{ color: '#52c41a' }}>${selectedOrder.netAmount.toFixed(2)}</strong>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrdersManagement;