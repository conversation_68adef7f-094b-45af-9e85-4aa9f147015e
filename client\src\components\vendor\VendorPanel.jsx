import React, { useState } from 'react';
import VendorLayout from './VendorLayout';
import ResponsiveVendorDashboard from './ResponsiveVendorDashboard';
import ProductsManagement from './sections/ProductsManagement';
import OrdersManagement from './sections/OrdersManagement';
import Analytics from './sections/Analytics';
import ReviewsManagement from './sections/ReviewsManagement';
import PayoutsManagement from './sections/PayoutsManagement';
import Settings from './sections/Settings';

const VendorPanel = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <ResponsiveVendorDashboard />;
      case 'products':
        return <ProductsManagement />;
      case 'orders':
        return <OrdersManagement />;
      case 'analytics':
        return <ResponsiveVendorDashboard />;
      case 'reviews':
        return <ReviewsManagement />;
      case 'payouts':
        return <PayoutsManagement />;
      case 'settings':
        return <Settings />;
      default:
        return <ResponsiveVendorDashboard />;
    }
  };

  return (
    <VendorLayout 
      activeKey={activeSection} 
      onMenuSelect={setActiveSection}
    >
      {renderContent()}
    </VendorLayout>
  );
};

export default VendorPanel;