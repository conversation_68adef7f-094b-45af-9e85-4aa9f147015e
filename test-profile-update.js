const fetch = require('node-fetch');

// Test profile update with a real token
async function testProfileUpdate() {
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODc5ZTIzYmFhMTliODZlYzZkYzE2MWUiLCJ1c2VyVHlwZSI6InVzZXIiLCJpYXQiOjE3Mzc0NjE5NzIsImV4cCI6MTczNzU0ODM3Mn0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Replace with actual token
    
    const profileData = {
        firstName: 'TestFirst',
        lastName: 'TestLast',
        country: 'United States',
        zipCode: '12345'
    };
    
    console.log('=== FRONTEND TEST START ===');
    console.log('Sending data:', profileData);
    console.log('Data JSON:', JSON.stringify(profileData));
    
    try {
        const response = await fetch('http://localhost:5000/api/auth/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(profileData)
        });
        
        const result = await response.json();
        console.log('Response status:', response.status);
        console.log('Response result:', result);
        
    } catch (error) {
        console.error('Test error:', error);
    }
}

testProfileUpdate();
