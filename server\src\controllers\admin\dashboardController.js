const { User, Vendor, Product, Order, Category } = require('../../models');

// Get dashboard overview statistics
const getDashboardStats = async (req, res) => {
  try {
    // Get current date and date ranges
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfYear = new Date(now.getFullYear(), 0, 1);

    // Get basic statistics
    const [
      userStats,
      vendorStats,
      productStats,
      orderStats,
      categoryStats
    ] = await Promise.all([
      User.getStatistics(),
      Vendor.getStatistics(),
      Product.getStatistics(),
      Order.getStatistics(),
      Category.getStatistics()
    ]);

    // Get recent activity
    const [
      recentOrders,
      recentUsers,
      recentVendors,
      recentProducts,
      topProducts,
      topCustomers,
      lowStockProducts
    ] = await Promise.all([
      Order.getRecent(5),
      User.find({ role: 'customer' }).sort({ createdAt: -1 }).limit(5).select('firstName lastName email createdAt'),
      Vendor.find().populate('user', 'firstName lastName email').sort({ createdAt: -1 }).limit(5).select('businessName verification.status createdAt'),
      Product.getRecent(5),
      Product.getTopSelling(5),
      Order.getTopCustomers(5),
      Product.getLowStock()
    ]);

    // Get revenue trends (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const revenueTrends = await Order.getOrdersByDateRange(thirtyDaysAgo, now);

    // Get user registration trends (last 30 days)
    const userRegistrationTrends = await User.getRecentRegistrations(30);
    const vendorRegistrationTrends = await Vendor.getRecentRegistrations(30);

    // Calculate growth percentages
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    
    const [
      currentMonthOrders,
      lastMonthOrders,
      currentMonthRevenue,
      lastMonthRevenue,
      currentMonthUsers,
      lastMonthUsers
    ] = await Promise.all([
      Order.countDocuments({ createdAt: { $gte: startOfMonth } }),
      Order.countDocuments({ 
        createdAt: { 
          $gte: new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1),
          $lt: startOfMonth
        }
      }),
      Order.aggregate([
        { $match: { createdAt: { $gte: startOfMonth } } },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      Order.aggregate([
        { 
          $match: { 
            createdAt: { 
              $gte: new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1),
              $lt: startOfMonth
            }
          }
        },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      User.countDocuments({ createdAt: { $gte: startOfMonth } }),
      User.countDocuments({ 
        createdAt: { 
          $gte: new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1),
          $lt: startOfMonth
        }
      })
    ]);

    const currentRevenue = currentMonthRevenue[0]?.total || 0;
    const previousRevenue = lastMonthRevenue[0]?.total || 0;

    const growthMetrics = {
      orders: {
        current: currentMonthOrders,
        previous: lastMonthOrders,
        growth: lastMonthOrders > 0 ? ((currentMonthOrders - lastMonthOrders) / lastMonthOrders * 100).toFixed(1) : 0
      },
      revenue: {
        current: currentRevenue,
        previous: previousRevenue,
        growth: previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue * 100).toFixed(1) : 0
      },
      users: {
        current: currentMonthUsers,
        previous: lastMonthUsers,
        growth: lastMonthUsers > 0 ? ((currentMonthUsers - lastMonthUsers) / lastMonthUsers * 100).toFixed(1) : 0
      }
    };

    // Get order status distribution
    const orderStatusDistribution = await Order.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          revenue: { $sum: '$pricing.total' }
        }
      }
    ]);

    // Get payment status distribution
    const paymentStatusDistribution = await Order.aggregate([
      {
        $group: {
          _id: '$payment.status',
          count: { $sum: 1 },
          amount: { $sum: '$pricing.total' }
        }
      }
    ]);

    // Get top categories by revenue
    const topCategories = await Category.find({ status: 'active' })
      .sort({ 'statistics.totalRevenue': -1 })
      .limit(5)
      .select('name statistics');

    // Get vendor performance summary
    const vendorPerformance = await Vendor.aggregate([
      {
        $group: {
          _id: '$verification.status',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$performance.totalRevenue' },
          averageRating: { $avg: '$performance.rating' }
        }
      }
    ]);

    const dashboardData = {
      overview: {
        users: userStats,
        vendors: vendorStats,
        products: productStats,
        orders: orderStats,
        categories: categoryStats
      },
      growth: growthMetrics,
      trends: {
        revenue: revenueTrends,
        userRegistrations: userRegistrationTrends,
        vendorRegistrations: vendorRegistrationTrends
      },
      distributions: {
        orderStatus: orderStatusDistribution,
        paymentStatus: paymentStatusDistribution,
        vendorPerformance: vendorPerformance
      },
      recentActivity: {
        orders: recentOrders,
        users: recentUsers,
        vendors: recentVendors,
        products: recentProducts
      },
      topPerformers: {
        products: topProducts,
        customers: topCustomers,
        categories: topCategories
      },
      alerts: {
        lowStockProducts: lowStockProducts,
        pendingVendors: vendorStats.pendingVendors,
        pendingOrders: orderStats.pendingOrders
      }
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get real-time metrics
const getRealTimeMetrics = async (req, res) => {
  try {
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Get real-time metrics
    const [
      todayOrders,
      todayRevenue,
      todayUsers,
      recentOrders,
      activeUsers,
      hourlyOrders
    ] = await Promise.all([
      Order.countDocuments({ createdAt: { $gte: startOfToday } }),
      Order.aggregate([
        { $match: { createdAt: { $gte: startOfToday } } },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      User.countDocuments({ createdAt: { $gte: startOfToday } }),
      Order.find({ createdAt: { $gte: oneHourAgo } })
        .populate('customer', 'firstName lastName')
        .select('orderNumber customer pricing.total status createdAt')
        .sort({ createdAt: -1 })
        .limit(10),
      User.countDocuments({ lastLogin: { $gte: oneHourAgo } }),
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: oneHourAgo }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%H:00',
                date: '$createdAt'
              }
            },
            count: { $sum: 1 },
            revenue: { $sum: '$pricing.total' }
          }
        },
        { $sort: { _id: 1 } }
      ])
    ]);

    const realTimeData = {
      today: {
        orders: todayOrders,
        revenue: todayRevenue[0]?.total || 0,
        newUsers: todayUsers,
        activeUsers: activeUsers
      },
      recent: {
        orders: recentOrders
      },
      hourly: hourlyOrders,
      timestamp: now
    };

    res.json({
      success: true,
      data: realTimeData
    });

  } catch (error) {
    console.error('Real-time metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch real-time metrics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get analytics data for charts
const getAnalytics = async (req, res) => {
  try {
    const { period = '30d', type = 'revenue' } = req.query;
    
    let startDate = new Date();
    let groupFormat = '%Y-%m-%d';
    
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        groupFormat = '%Y-%m-%d';
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        groupFormat = '%Y-%m';
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    let analyticsData = {};

    switch (type) {
      case 'revenue':
        analyticsData = await Order.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate },
              'payment.status': 'completed'
            }
          },
          {
            $group: {
              _id: {
                $dateToString: {
                  format: groupFormat,
                  date: '$createdAt'
                }
              },
              revenue: { $sum: '$pricing.total' },
              orders: { $sum: 1 }
            }
          },
          { $sort: { _id: 1 } }
        ]);
        break;

      case 'users':
        analyticsData = await User.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: {
                $dateToString: {
                  format: groupFormat,
                  date: '$createdAt'
                }
              },
              customers: {
                $sum: { $cond: [{ $eq: ['$role', 'customer'] }, 1, 0] }
              },
              vendors: {
                $sum: { $cond: [{ $eq: ['$role', 'vendor'] }, 1, 0] }
              },
              total: { $sum: 1 }
            }
          },
          { $sort: { _id: 1 } }
        ]);
        break;

      case 'products':
        analyticsData = await Product.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: {
                $dateToString: {
                  format: groupFormat,
                  date: '$createdAt'
                }
              },
              active: {
                $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
              },
              draft: {
                $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
              },
              total: { $sum: 1 }
            }
          },
          { $sort: { _id: 1 } }
        ]);
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid analytics type'
        });
    }

    res.json({
      success: true,
      data: {
        period,
        type,
        analytics: analyticsData
      }
    });

  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get system health status
const getSystemHealth = async (req, res) => {
  try {
    const mongoose = require('mongoose');
    
    // Check database connection
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
    
    // Get database stats
    const dbStats = await mongoose.connection.db.stats();
    
    // Check for any critical issues
    const criticalIssues = [];
    
    // Check for low stock products
    const lowStockCount = await Product.countDocuments({
      'inventory.trackQuantity': true,
      $expr: { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }
    });
    
    if (lowStockCount > 0) {
      criticalIssues.push({
        type: 'low_stock',
        message: `${lowStockCount} products are low in stock`,
        severity: 'warning'
      });
    }
    
    // Check for pending vendor verifications
    const pendingVendors = await Vendor.countDocuments({
      'verification.status': 'pending'
    });
    
    if (pendingVendors > 10) {
      criticalIssues.push({
        type: 'pending_verifications',
        message: `${pendingVendors} vendor verifications are pending`,
        severity: 'warning'
      });
    }
    
    // Check for failed payments
    const failedPayments = await Order.countDocuments({
      'payment.status': 'failed',
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });
    
    if (failedPayments > 5) {
      criticalIssues.push({
        type: 'failed_payments',
        message: `${failedPayments} payments failed in the last 24 hours`,
        severity: 'error'
      });
    }

    const systemHealth = {
      status: criticalIssues.length === 0 ? 'healthy' : 'warning',
      database: {
        status: dbStatus,
        collections: dbStats.collections,
        dataSize: dbStats.dataSize,
        indexSize: dbStats.indexSize
      },
      issues: criticalIssues,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date()
    };

    res.json({
      success: true,
      data: systemHealth
    });

  } catch (error) {
    console.error('System health error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system health',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getDashboardStats,
  getRealTimeMetrics,
  getAnalytics,
  getSystemHealth
};