# Product Management API Documentation

This document provides comprehensive documentation for the product management system in the multi-vendor eCommerce platform.

## Overview

The product management system allows vendors to create, manage, and sell their products while providing administrators with oversight capabilities and customers with a seamless shopping experience.

## Features

- ✅ **Vendor Product Management**: Create, update, delete products
- ✅ **Multiple Image Support**: Upload up to 8 product images
- ✅ **Inventory Management**: Track stock levels and low stock alerts
- ✅ **Pricing Management**: Base price, sale price, and tax configuration
- ✅ **Product Variants**: Support for product variations (size, color, etc.)
- ✅ **SEO Optimization**: Meta titles, descriptions, and keywords
- ✅ **Admin Oversight**: Product approval and management
- ✅ **Public Product Listing**: Customer-facing product catalog
- ✅ **Search and Filtering**: Advanced product search capabilities
- ✅ **Product Statistics**: Analytics and reporting

## API Endpoints

### Vendor Endpoints

#### Create Product
```http
POST /api/vendor/products
Authorization: Bearer {vendor_token}
Content-Type: multipart/form-data
```

**Request Body:**
```json
{
  "name": "Product Name",
  "description": "Detailed product description",
  "shortDescription": "Brief description",
  "category": "category_id",
  "subcategory": "subcategory_id",
  "brand": "Brand Name",
  "sku": "UNIQUE-SKU-001",
  "pricing": {
    "basePrice": 99.99,
    "salePrice": 89.99,
    "currency": "USD",
    "taxClass": "standard",
    "taxRate": 8.5
  },
  "inventory": {
    "trackQuantity": true,
    "quantity": 100,
    "lowStockThreshold": 10,
    "allowBackorders": false
  },
  "specifications": {
    "weight": {
      "value": 1.5,
      "unit": "kg"
    },
    "dimensions": {
      "length": 20,
      "width": 15,
      "height": 5,
      "unit": "cm"
    }
  },
  "shipping": {
    "weight": 1.6,
    "shippingClass": "standard",
    "freeShipping": false,
    "shippingCost": 9.99
  },
  "seo": {
    "metaTitle": "SEO Title",
    "metaDescription": "SEO Description",
    "keywords": ["keyword1", "keyword2"]
  },
  "status": "active",
  "visibility": "public",
  "featured": false,
  "tags": ["tag1", "tag2"],
  "attributes": [
    {
      "name": "Color",
      "value": "Blue"
    }
  ]
}
```

**Files:**
- `productImages`: Array of image files (max 8 images, 2MB each)

#### Get Vendor Products
```http
GET /api/vendor/products?page=1&limit=10&status=active&search=keyword
Authorization: Bearer {vendor_token}
```

#### Get Single Product
```http
GET /api/vendor/products/{product_id}
Authorization: Bearer {vendor_token}
```

#### Update Product
```http
PUT /api/vendor/products/{product_id}
Authorization: Bearer {vendor_token}
Content-Type: multipart/form-data
```

#### Delete Product
```http
DELETE /api/vendor/products/{product_id}
Authorization: Bearer {vendor_token}
```

#### Update Stock
```http
PATCH /api/vendor/products/{product_id}/stock
Authorization: Bearer {vendor_token}
Content-Type: application/json

{
  "quantity": 50,
  "operation": "set" // "set", "add", or "subtract"
}
```

#### Bulk Operations
```http
POST /api/vendor/products/bulk
Authorization: Bearer {vendor_token}
Content-Type: application/json

{
  "productIds": ["id1", "id2"],
  "action": "activate" // "activate", "deactivate", "delete", "archive"
}
```

#### Remove Product Image
```http
DELETE /api/vendor/products/{product_id}/images
Authorization: Bearer {vendor_token}
Content-Type: application/json

{
  "imageUrl": "/uploads/images/products/image.jpg"
}
```

#### Get Product Statistics
```http
GET /api/vendor/products/stats
Authorization: Bearer {vendor_token}
```

### Admin Endpoints

#### Get All Products
```http
GET /api/admin/products?page=1&limit=20&status=active&vendor=vendor_id
Authorization: Bearer {admin_token}
```

#### Get Product Statistics
```http
GET /api/admin/products/stats
Authorization: Bearer {admin_token}
```

#### Update Product Status
```http
PATCH /api/admin/products/{product_id}/status
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "status": "active",
  "reason": "Approved after review"
}
```

#### Toggle Featured Status
```http
PATCH /api/admin/products/{product_id}/featured
Authorization: Bearer {admin_token}
```

#### Get Low Stock Products
```http
GET /api/admin/products/low-stock
Authorization: Bearer {admin_token}
```

#### Get Products by Vendor
```http
GET /api/admin/products/vendor/{vendor_id}
Authorization: Bearer {admin_token}
```

### Public Endpoints

#### Get Products
```http
GET /api/public/products?page=1&limit=12&category=cat_id&minPrice=10&maxPrice=100
```

#### Get Single Product
```http
GET /api/public/products/{product_id_or_slug}
```

#### Get Featured Products
```http
GET /api/public/products/featured?limit=8
```

#### Get New Arrivals
```http
GET /api/public/products/new-arrivals?limit=8&days=30
```

#### Get Best Selling
```http
GET /api/public/products/best-selling?limit=8
```

#### Search Products
```http
GET /api/public/products/search?q=search_term&page=1&limit=12
```

#### Get Products by Category
```http
GET /api/public/products/category/{category_id}?page=1&limit=12
```

#### Get Products by Vendor
```http
GET /api/public/products/vendor/{vendor_id}?page=1&limit=12
```

## Data Models

### Product Schema

```javascript
{
  vendor: ObjectId, // Reference to Vendor
  name: String, // Required, 2-200 characters
  slug: String, // Auto-generated, unique
  description: String, // Required, 10-2000 characters
  shortDescription: String, // Optional, max 500 characters
  category: ObjectId, // Required, reference to Category
  subcategory: ObjectId, // Optional, reference to Category
  brand: String, // Optional, max 100 characters
  sku: String, // Required, unique, uppercase
  barcode: String, // Optional
  images: [{
    url: String, // Required
    alt: String, // Optional
    isPrimary: Boolean, // Default false
    order: Number // Default 0
  }],
  pricing: {
    basePrice: Number, // Required, min 0
    salePrice: Number, // Optional, min 0
    costPrice: Number, // Optional, min 0
    currency: String, // Default 'USD'
    taxClass: String, // Enum: standard, reduced, zero, exempt
    taxRate: Number // 0-100, default 0
  },
  inventory: {
    trackQuantity: Boolean, // Default true
    quantity: Number, // Required if trackQuantity true
    lowStockThreshold: Number, // Default 5
    allowBackorders: Boolean, // Default false
    stockStatus: String // Enum: in_stock, out_of_stock, on_backorder
  },
  variants: [{
    name: String, // Required
    options: [{
      name: String, // Required
      value: String, // Required
      priceModifier: Number, // Default 0
      sku: String, // Optional
      quantity: Number, // Default 0
      image: String // Optional
    }]
  }],
  attributes: [{
    name: String, // Required
    value: String, // Required
    unit: String // Optional
  }],
  specifications: {
    weight: {
      value: Number,
      unit: String // Enum: kg, g, lb, oz
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      unit: String // Enum: cm, m, in, ft
    },
    material: String,
    color: String,
    size: String
  },
  shipping: {
    weight: Number,
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    },
    shippingClass: String, // Enum: standard, heavy, fragile, digital
    freeShipping: Boolean, // Default false
    shippingCost: Number // Default 0
  },
  seo: {
    metaTitle: String, // Max 60 characters
    metaDescription: String, // Max 160 characters
    keywords: [String],
    canonicalUrl: String
  },
  status: String, // Enum: draft, active, inactive, archived
  visibility: String, // Enum: public, private, password_protected
  featured: Boolean, // Default false
  tags: [String],
  reviews: {
    averageRating: Number, // 0-5, default 0
    totalReviews: Number, // Default 0
    ratingDistribution: {
      1: Number, 2: Number, 3: Number, 4: Number, 5: Number
    }
  },
  sales: {
    totalSold: Number, // Default 0
    totalRevenue: Number, // Default 0
    lastSaleDate: Date
  },
  publishedAt: Date,
  lastModified: Date,
  modifiedBy: ObjectId // Reference to User
}
```

## Image Upload

### Supported Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)

### Specifications
- Maximum file size: 2MB per image
- Maximum images per product: 8
- Automatic resizing and optimization
- CDN-ready URLs

### Upload Process
1. Images are uploaded to `/uploads/images/products/` directory
2. Unique filenames are generated using timestamp and random hash
3. Image URLs are stored in the product document
4. First uploaded image is automatically set as primary

## Validation Rules

### Product Creation
- Name: Required, 2-200 characters, alphanumeric with special chars
- Description: Required, 10-2000 characters
- Category: Required, valid ObjectId
- SKU: Required, 3-50 characters, uppercase letters/numbers/hyphens/underscores
- Base Price: Required, positive number
- Sale Price: Optional, must be less than base price if provided

### Stock Management
- Quantity: Non-negative integer
- Low stock threshold: Non-negative integer
- Track quantity: Boolean

### SEO Fields
- Meta title: Max 60 characters
- Meta description: Max 160 characters
- Keywords: Array of strings, max 50 characters each

## Error Handling

### Common Error Responses

#### Validation Error (400)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "name",
      "message": "Product name is required",
      "value": ""
    }
  ]
}
```

#### Unauthorized (401)
```json
{
  "success": false,
  "message": "Access token is required"
}
```

#### Forbidden (403)
```json
{
  "success": false,
  "message": "Vendor verification required to add products"
}
```

#### Not Found (404)
```json
{
  "success": false,
  "message": "Product not found"
}
```

#### Server Error (500)
```json
{
  "success": false,
  "message": "Failed to create product",
  "error": "Detailed error message (development only)"
}
```

## Testing

### Running Tests
```bash
# Run all product tests
npm test -- --grep "Product Management"

# Run specific test file
npm test src/tests/productTests.js

# Run product creation test
node test-product-creation.js
```

### Test Coverage
- ✅ Product creation with validation
- ✅ Product listing with pagination
- ✅ Product updates and stock management
- ✅ Image upload and management
- ✅ Admin product oversight
- ✅ Public product access
- ✅ Search and filtering
- ✅ Error handling and validation

## Security Considerations

### Authentication & Authorization
- Vendor routes require valid vendor authentication
- Admin routes require admin privileges
- Public routes are accessible without authentication
- Product ownership verification for vendor operations

### Input Validation
- Comprehensive validation for all input fields
- File upload validation (type, size, count)
- SQL injection prevention through Mongoose
- XSS protection through input sanitization

### File Upload Security
- File type validation
- File size limits
- Secure file naming
- Directory traversal prevention

## Performance Optimizations

### Database Indexing
- Compound indexes on frequently queried fields
- Text search indexes for product search
- Category and vendor indexes for filtering

### Caching Strategy
- Product listings cached for public access
- Category-based caching
- Image CDN integration ready

### Pagination
- Efficient pagination with skip/limit
- Configurable page sizes
- Total count optimization

## Best Practices

### For Vendors
1. Use descriptive product names and detailed descriptions
2. Upload high-quality images (first image as primary)
3. Set accurate inventory levels and low stock thresholds
4. Use relevant tags and categories
5. Optimize SEO fields for better discoverability

### For Administrators
1. Regularly review and approve new products
2. Monitor low stock alerts
3. Feature high-quality products
4. Maintain category structure
5. Review product performance metrics

### For Developers
1. Always validate input data
2. Handle errors gracefully
3. Use transactions for critical operations
4. Implement proper logging
5. Follow RESTful API conventions

## Troubleshooting

### Common Issues

#### Product Creation Fails
- Check vendor verification status
- Verify category exists and is active
- Ensure SKU is unique
- Validate required fields

#### Image Upload Issues
- Check file size (max 2MB)
- Verify file format (JPEG, PNG, GIF)
- Ensure upload directory permissions
- Check maximum image count (8 images)

#### Search Not Working
- Verify text indexes are created
- Check search query length (min 2 characters)
- Ensure products are active and public

#### Stock Updates Failing
- Verify product ownership
- Check quantity validation (non-negative)
- Ensure inventory tracking is enabled

## API Rate Limiting

### Limits
- Vendor endpoints: 100 requests per minute
- Admin endpoints: 200 requests per minute
- Public endpoints: 1000 requests per minute
- Image upload: 10 uploads per minute

### Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Changelog

### Version 1.0.0
- Initial product management system
- Vendor product CRUD operations
- Admin oversight capabilities
- Public product catalog
- Image upload support
- Search and filtering
- Comprehensive validation
- Test suite implementation

---

For additional support or questions, please refer to the main project documentation or contact the development team.