const express = require('express');
const router = express.Router();

// Import middleware
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');

// Import validation middleware
const {
  validateProductList,
  validateProductId,
  validateBulkOperation
} = require('../../middleware/validation/productValidation');

// Import controller
const {
  getAllProducts,
  getProduct,
  updateProductStatus,
  toggleFeatured,
  bulkOperation,
  getProductStats,
  getLowStockProducts,
  getProductsByVendor,
  getPendingApproval,
  approveProduct,
  rejectProduct,
  requestProductChanges
} = require('../../controllers/admin/productController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType('admin'));

/**
 * @route   GET /api/admin/products/stats
 * @desc    Get product statistics for admin dashboard
 * @access  Private (Admin)
 */
router.get('/stats', getProductStats);

/**
 * @route   GET /api/admin/products/low-stock
 * @desc    Get low stock products
 * @access  Private (Admin)
 */
router.get('/low-stock', getLowStockProducts);

/**
 * @route   GET /api/admin/products/vendor/:vendorId
 * @desc    Get products by vendor
 * @access  Private (Admin)
 */
router.get('/vendor/:vendorId', getProductsByVendor);

/**
 * @route   GET /api/admin/products
 * @desc    Get all products with pagination and filters
 * @access  Private (Admin)
 */
router.get('/', validateProductList, getAllProducts);

/**
 * @route   GET /api/admin/products/:id
 * @desc    Get single product by ID
 * @access  Private (Admin)
 */
router.get('/:id', validateProductId, getProduct);

/**
 * @route   PATCH /api/admin/products/:id/status
 * @desc    Update product status
 * @access  Private (Admin)
 */
router.patch('/:id/status', validateProductId, updateProductStatus);

/**
 * @route   PATCH /api/admin/products/:id/featured
 * @desc    Toggle product featured status
 * @access  Private (Admin)
 */
router.patch('/:id/featured', validateProductId, toggleFeatured);

/**
 * @route   POST /api/admin/products/bulk
 * @desc    Bulk operations on products
 * @access  Private (Admin)
 */
router.post('/bulk', validateBulkOperation, bulkOperation);

/**
 * @route   GET /api/admin/products/pending-approval
 * @desc    Get products pending approval
 * @access  Private (Admin)
 */
router.get('/pending-approval', getPendingApproval);

/**
 * @route   PATCH /api/admin/products/:id/approve
 * @desc    Approve product
 * @access  Private (Admin)
 */
router.patch('/:id/approve', validateProductId, approveProduct);

/**
 * @route   PATCH /api/admin/products/:id/reject
 * @desc    Reject product
 * @access  Private (Admin)
 */
router.patch('/:id/reject', validateProductId, rejectProduct);

/**
 * @route   PATCH /api/admin/products/:id/request-changes
 * @desc    Request changes for product
 * @access  Private (Admin)
 */
router.patch('/:id/request-changes', validateProductId, requestProductChanges);

module.exports = router;