#!/usr/bin/env node

require('dotenv').config({ path: './server/.env' });
const mongoose = require('mongoose');

const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';

async function testConnection() {
    console.log('🔧 Testing MongoDB Connection\n');
    console.log('📍 Connecting to:', DB_URL.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@'));
    
    try {
        await mongoose.connect(DB_URL, {
            serverSelectionTimeoutMS: 10000,
            socketTimeoutMS: 45000,
        });
        
        console.log('✅ Database connection successful!');
        
        // Test basic operations
        console.log('\n🔍 Testing basic database operations...');
        
        // List databases
        const admin = mongoose.connection.db.admin();
        const dbs = await admin.listDatabases();
        console.log('📋 Available databases:', dbs.databases.map(db => db.name).join(', '));
        
        // Test collection access
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('📁 Collections in current database:', collections.map(col => col.name).join(', ') || 'None (new database)');
        
        console.log('\n✅ All tests passed! Your database is ready.');
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        
        if (error.message.includes('ECONNREFUSED')) {
            console.log('\n💡 Troubleshooting:');
            console.log('   1. Make sure MongoDB is installed and running');
            console.log('   2. Run: node setup-mongodb.js');
            console.log('   3. Or start MongoDB manually: mongod');
        } else if (error.message.includes('authentication failed')) {
            console.log('\n💡 Authentication issue:');
            console.log('   1. Check your MongoDB credentials');
            console.log('   2. For local development, use: mongodb://localhost:27017/multi-vendor-ecommerce');
        } else if (error.message.includes('timeout')) {
            console.log('\n💡 Connection timeout:');
            console.log('   1. Check your internet connection (for Atlas)');
            console.log('   2. Verify MongoDB Atlas IP whitelist');
            console.log('   3. Try using local MongoDB instead');
        }
    } finally {
        await mongoose.connection.close();
        console.log('\n📴 Connection closed');
        process.exit(0);
    }
}

testConnection();