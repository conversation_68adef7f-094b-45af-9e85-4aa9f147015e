const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const User = require('../../schema/userSchema');
const emailService = require('../utils/emailService');

// Generate JWT token
const generateToken = (userId, userType) => {
    return jwt.sign(
        { userId, userType },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
};

// Generate refresh token
const generateRefreshToken = (userId) => {
    return jwt.sign(
        { userId, type: 'refresh' },
        process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
        { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d' }
    );
};

class AuthController {
    // Register new user
    async register(req, res) {
        try {
            const {
                firstName,
                lastName,
                email,
                password,
                userType,
                businessName,
                businessType,
                contactPerson
            } = req.body;

            // Check if user already exists
            const existingUser = await User.findByEmail(email);
            if (existingUser) {
                return res.status(400).json({
                    success: false,
                    message: 'User with this email already exists'
                });
            }

            // Create user data object
            const userData = {
                email: email.toLowerCase(),
                password,
                userType: userType || 'user'
            };

            // Add user-specific fields
            if (userType === 'user') {
                userData.firstName = firstName;
                userData.lastName = lastName;
            } else if (userType === 'vendor') {
                userData.businessName = businessName;
                userData.businessType = businessType;
                userData.contactPerson = contactPerson;
            }

            // Create new user
            const user = new User(userData);
            
            // Generate email verification token
            const verificationToken = user.generateEmailVerificationToken();
            
            await user.save();

            // Send verification email
            await emailService.sendVerificationEmail(user, verificationToken);

            // Generate tokens
            const token = generateToken(user._id, user.userType);
            const refreshToken = generateRefreshToken(user._id);

            // Remove sensitive data from response
            const userResponse = user.toObject();
            delete userResponse.password;
            delete userResponse.emailVerification.verificationToken;
            delete userResponse.passwordReset;

            res.status(201).json({
                success: true,
                message: 'User registered successfully. Please check your email for verification.',
                data: {
                    user: userResponse,
                    token,
                    refreshToken
                }
            });

        } catch (error) {
            console.error('Registration error:', error);
            
            if (error.code === 11000) {
                return res.status(400).json({
                    success: false,
                    message: 'Email already exists'
                });
            }

            if (error.name === 'ValidationError') {
                const errors = Object.values(error.errors).map(err => err.message);
                return res.status(400).json({
                    success: false,
                    message: 'Validation error',
                    errors
                });
            }

            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Login user
    async login(req, res) {
        try {
            const { email, password, rememberMe } = req.body;
            const userAgent = req.get('User-Agent');
            const ip = req.ip || req.connection.remoteAddress;

            // Find user and include password for comparison
            const user = await User.findByEmail(email).select('+password');
            
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid email or password'
                });
            }

            // Check if account is locked
            if (user.isLocked) {
                return res.status(423).json({
                    success: false,
                    message: 'Account is temporarily locked due to too many failed login attempts'
                });
            }

            // Check if account is blocked
            if (user.isBlocked) {
                return res.status(403).json({
                    success: false,
                    message: 'Account is blocked. Please contact support.'
                });
            }

            // Verify password
            const isPasswordValid = await user.comparePassword(password);
            
            if (!isPasswordValid) {
                await user.incLoginAttempts();
                return res.status(401).json({
                    success: false,
                    message: 'Invalid email or password'
                });
            }

            // Reset login attempts on successful login
            if (user.security.loginAttempts > 0) {
                await user.resetLoginAttempts();
            }

            // Update login information
            user.security.lastLogin = new Date();
            user.security.lastLoginIP = ip;
            user.lastActiveAt = new Date();
            await user.save();

            // Generate tokens
            const tokenExpiry = rememberMe ? '30d' : '7d';
            const token = jwt.sign(
                { userId: user._id, userType: user.userType },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: tokenExpiry }
            );
            const refreshToken = generateRefreshToken(user._id);

            // Remove sensitive data from response
            const userResponse = user.toObject();
            delete userResponse.password;
            delete userResponse.emailVerification.verificationToken;
            delete userResponse.passwordReset;
            delete userResponse.twoFactorAuth.secret;

            res.json({
                success: true,
                message: 'Login successful',
                data: {
                    user: userResponse,
                    token,
                    refreshToken
                }
            });

        } catch (error) {
            console.error('Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Verify email
    async verifyEmail(req, res) {
        try {
            const { token, email } = req.query;

            if (!token || !email) {
                return res.status(400).json({
                    success: false,
                    message: 'Token and email are required'
                });
            }

            // First, check if user exists and is already verified
            const existingUser = await User.findByEmail(email);
            if (!existingUser) {
                return res.status(400).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Check if email is already verified
            if (existingUser.emailVerification.isVerified) {
                return res.status(200).json({
                    success: true,
                    message: 'Email is already verified',
                    alreadyVerified: true
                });
            }

            // Hash the token to compare with stored hash
            const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

            // Find user with matching token and email
            const user = await User.findOne({
                email: email.toLowerCase(),
                'emailVerification.verificationToken': hashedToken,
                'emailVerification.verificationTokenExpires': { $gt: Date.now() }
            });

            if (!user) {
                // Check if token has expired
                const userWithExpiredToken = await User.findOne({
                    email: email.toLowerCase(),
                    'emailVerification.verificationToken': hashedToken
                });

                if (userWithExpiredToken) {
                    return res.status(400).json({
                        success: false,
                        message: 'Verification token has expired. Please request a new verification email.',
                        expired: true
                    });
                }

                return res.status(400).json({
                    success: false,
                    message: 'Invalid verification token. The token may have already been used or is incorrect.',
                    invalid: true
                });
            }

            // Update user verification status
            user.emailVerification.isVerified = true;
            user.emailVerification.verifiedAt = new Date();
            user.emailVerification.verificationToken = undefined;
            user.emailVerification.verificationTokenExpires = undefined;
            
            await user.save();

            // Send welcome email
            try {
                await emailService.sendWelcomeEmail(user);
            } catch (emailError) {
                console.error('Welcome email error:', emailError);
                // Don't fail verification if welcome email fails
            }

            res.json({
                success: true,
                message: 'Email verified successfully',
                verifiedAt: user.emailVerification.verifiedAt
            });

        } catch (error) {
            console.error('Email verification error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Resend verification email
    async resendVerificationEmail(req, res) {
        try {
            const { email } = req.body;

            const user = await User.findByEmail(email);
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            if (user.emailVerification.isVerified) {
                return res.status(400).json({
                    success: false,
                    message: 'Email is already verified'
                });
            }

            // Generate new verification token
            const verificationToken = user.generateEmailVerificationToken();
            await user.save();

            // Send verification email
            await emailService.sendVerificationEmail(user, verificationToken);

            res.json({
                success: true,
                message: 'Verification email sent successfully'
            });

        } catch (error) {
            console.error('Resend verification error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Forgot password
    async forgotPassword(req, res) {
        try {
            const { email } = req.body;

            const user = await User.findByEmail(email);
            if (!user) {
                // Don't reveal if email exists or not
                return res.json({
                    success: true,
                    message: 'If an account with that email exists, a password reset link has been sent.'
                });
            }

            // Generate password reset token
            const resetToken = user.generatePasswordResetToken();
            await user.save();

            // Send password reset email
            await emailService.sendPasswordResetEmail(user, resetToken);

            
            res.json({
                success: true,
                message: 'If an account with that email exists, a password reset link has been sent.'
            });

        } catch (error) {
            console.error('Forgot password error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Reset password
    async resetPassword(req, res) {
        try {
            const { token, email, newPassword } = req.body;

            if (!token || !email || !newPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'Token, email, and new password are required'
                });
            }

            // Hash the token to compare with stored hash
            const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

            // Find user with matching token and email
            const user = await User.findOne({
                email: email.toLowerCase(),
                'passwordReset.token': hashedToken,
                'passwordReset.tokenExpires': { $gt: Date.now() }
            });

            if (!user) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid or expired reset token'
                });
            }

            // Update password
            user.password = newPassword;
            user.passwordReset.token = undefined;
            user.passwordReset.tokenExpires = undefined;
            user.passwordReset.lastResetAt = new Date();
            
            // Reset login attempts
            user.security.loginAttempts = 0;
            user.security.lockUntil = undefined;
            
            await user.save();

            res.json({
                success: true,
                message: 'Password reset successfully'
            });

        } catch (error) {
            console.error('Reset password error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    
    // Refresh token
    async refreshToken(req, res) {
        try {
            const { refreshToken } = req.body;

            if (!refreshToken) {
                return res.status(401).json({
                    success: false,
                    message: 'Refresh token is required'
                });
            }

            // Verify refresh token
            const decoded = jwt.verify(
                refreshToken,
                process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key'
            );

            if (decoded.type !== 'refresh') {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid token type'
                });
            }

            // Find user
            const user = await User.findById(decoded.userId);
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Generate new tokens
            const newToken = generateToken(user._id, user.userType);
            const newRefreshToken = generateRefreshToken(user._id);

            res.json({
                success: true,
                data: {
                    token: newToken,
                    refreshToken: newRefreshToken
                }
            });

        } catch (error) {
            console.error('Refresh token error:', error);
            res.status(401).json({
                success: false,
                message: 'Invalid refresh token'
            });
        }
    }

    // Logout
    async logout(req, res) {
        try {
            // In a production environment, you might want to blacklist the token
            // For now, we'll just return a success response
            res.json({
                success: true,
                message: 'Logged out successfully'
            });
        } catch (error) {
            console.error('Logout error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Get current user profile
    async getProfile(req, res) {
        try {
            const { userId } = req.user;

            const user = await User.findById(userId);
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Remove sensitive data
            const userResponse = user.toObject();
            delete userResponse.password;
            delete userResponse.emailVerification.verificationToken;
            delete userResponse.passwordReset;
            delete userResponse.twoFactorAuth.secret;

            res.json({
                success: true,
                data: { user: userResponse }
            });

        } catch (error) {
            console.error('Get profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Update user profile
    async updateProfile(req, res) {
        try {
            const { userId } = req.user;
            const updateData = req.body || {};

            console.log('=== PROFILE UPDATE DEBUG ===');
            console.log('- User ID:', userId);
            console.log('- Request body:', JSON.stringify(req.body, null, 2));
            console.log('- Update data:', JSON.stringify(updateData, null, 2));
            console.log('- Content-Type:', req.headers['content-type']);
            console.log('- Request method:', req.method);

            // Validate that we have update data
            if (!updateData || Object.keys(updateData).length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'No update data provided'
                });
            }

            // Find user
            const user = await User.findById(userId);
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            console.log('User found - ID:', user._id, 'Type:', user.userType, 'Email:', user.email);

            // Handle preferences if it's a JSON string (from form data)
            if (updateData.preferences && typeof updateData.preferences === 'string') {
                try {
                    updateData.preferences = JSON.parse(updateData.preferences);
                    console.log('Parsed preferences from string:', updateData.preferences);
                } catch (error) {
                    console.log('Failed to parse preferences JSON:', error.message);
                    delete updateData.preferences; // Remove invalid preferences
                }
            }

            // Remove email field for security (email changes should be handled separately)
            if (updateData.email) {
                console.log('Removing email field from update data for security');
                delete updateData.email;
            }

            // Update allowed fields based on user type
            const allowedFields = user.userType === 'vendor' 
                ? ['businessName', 'businessType', 'contactPerson', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country', 'phone', 'bio', 'displayName', 'website']
                : ['firstName', 'lastName', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country', 'phone', 'bio', 'displayName', 'website'];

            console.log('Allowed fields for', user.userType + ':', allowedFields);

            // Filter update data to only include allowed fields
            const filteredUpdateData = {};

            allowedFields.forEach(field => {
                if (updateData.hasOwnProperty(field) && updateData[field] !== undefined) {
                    // Handle empty strings - convert to null for optional fields
                    filteredUpdateData[field] = updateData[field] === '' ? null : updateData[field];
                }
            });

            // Handle preferences separately
            if (updateData.preferences && typeof updateData.preferences === 'object') {
                console.log('Processing preferences:', updateData.preferences);
                
                // Validate and set individual preference fields
                if (updateData.preferences.language !== undefined) {
                    const validLanguages = ['en', 'es', 'fr', 'de'];
                    if (validLanguages.includes(updateData.preferences.language)) {
                        filteredUpdateData['preferences.language'] = updateData.preferences.language;
                    } else {
                        console.log('Invalid language:', updateData.preferences.language);
                    }
                }
                
                if (updateData.preferences.currency !== undefined) {
                    const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD'];
                    if (validCurrencies.includes(updateData.preferences.currency)) {
                        filteredUpdateData['preferences.currency'] = updateData.preferences.currency;
                    } else {
                        console.log('Invalid currency:', updateData.preferences.currency);
                    }
                }
                
                if (updateData.preferences.timezone !== undefined) {
                    if (typeof updateData.preferences.timezone === 'string' && updateData.preferences.timezone.length <= 50) {
                        filteredUpdateData['preferences.timezone'] = updateData.preferences.timezone;
                    } else {
                        console.log('Invalid timezone:', updateData.preferences.timezone);
                    }
                }
                
                if (updateData.preferences.notifications !== undefined && typeof updateData.preferences.notifications === 'object') {
                    filteredUpdateData['preferences.notifications'] = updateData.preferences.notifications;
                }
            }

            console.log('Filtered update data:', JSON.stringify(filteredUpdateData, null, 2));

            // Check if there are any fields to update
            if (Object.keys(filteredUpdateData).length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'No valid fields to update. Please provide at least one valid field with a non-empty value.',
                    allowedFields: allowedFields,
                    receivedFields: Object.keys(updateData).filter(field => field !== 'email'), // Exclude email from received fields
                    hint: 'Email updates are not allowed for security reasons. Other fields should be valid.',
                    examples: {
                        user: { address: '123 Main St', city: 'New York' },
                        vendor: { businessName: 'My Business', address: '456 Oak Ave' }
                    }
                });
            }

            // Update user using $set to handle nested fields properly
            console.log('Updating user with data:', filteredUpdateData);
            const updateResult = await User.findByIdAndUpdate(
                userId, 
                { $set: filteredUpdateData }, 
                { 
                    new: true,
                    runValidators: true // Ensure validation runs on update
                }
            );

            if (!updateResult) {
                return res.status(404).json({
                    success: false,
                    message: 'Failed to update user'
                });
            }

            // Fetch updated user to ensure we have the latest data
            const updatedUser = await User.findById(userId);

            // Remove sensitive data from response
            const userResponse = updatedUser.toObject();
            delete userResponse.password;
            delete userResponse.emailVerification.verificationToken;
            delete userResponse.passwordReset;
            delete userResponse.twoFactorAuth.secret;

            console.log('Profile updated successfully for user:', userId);

            res.json({
                success: true,
                message: 'Profile updated successfully',
                data: { 
                    user: userResponse,
                    updatedFields: Object.keys(filteredUpdateData)
                }
            });

        } catch (error) {
            console.error('Update profile error:', error);
            
            if (error.name === 'ValidationError') {
                const errors = Object.values(error.errors).map(err => ({
                    field: err.path,
                    message: err.message,
                    value: err.value
                }));
                return res.status(400).json({
                    success: false,
                    message: 'Validation error',
                    errors
                });
            }

            if (error.name === 'CastError') {
                return res.status(400).json({
                    success: false,
                    message: `Invalid value for field: ${error.path}`,
                    field: error.path,
                    value: error.value
                });
            }

            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    // Change password
    async changePassword(req, res) {
        try {
            const { userId } = req.user;
            const { currentPassword, newPassword } = req.body;

            console.log('Change password request for user:', userId);
            console.log('Request body:', { currentPassword: '***', newPassword: '***' });

            // Find user with password
            const user = await User.findById(userId).select('+password');
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Verify current password
            const isCurrentPasswordValid = await user.comparePassword(currentPassword);
            if (!isCurrentPasswordValid) {
                return res.status(400).json({
                    success: false,
                    message: 'Current password is incorrect'
                });
            }

            // Check if new password is different from current
            const isSamePassword = await user.comparePassword(newPassword);
            if (isSamePassword) {
                return res.status(400).json({
                    success: false,
                    message: 'New password must be different from current password'
                });
            }

            // Update password
            user.password = newPassword;
            user.security.passwordChangedAt = new Date();
            await user.save();

            console.log('Password changed successfully for user:', userId);

            res.json({
                success: true,
                message: 'Password changed successfully'
            });

        } catch (error) {
            console.error('Change password error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
}

module.exports = new AuthController();