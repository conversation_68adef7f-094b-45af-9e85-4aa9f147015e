<!DOCTYPE html>
<html>
<head>
    <title>Test Profile Update</title>
</head>
<body>
    <h1>Test Profile Update</h1>
    <p>Open browser console and check the logs when you test the profile update functionality.</p>
    
    <h2>Steps to test:</h2>
    <ol>
        <li>Login to the application</li>
        <li>Go to Profile page</li>
        <li>Click "Edit Profile"</li>
        <li>Change any field (e.g., first name from "<PERSON>" to "<PERSON>")</li>
        <li>Click "Save Changes"</li>
        <li>Check browser console for logs</li>
        <li>Check server logs for backend processing</li>
    </ol>
    
    <h2>Expected logs in browser console:</h2>
    <pre>
=== AuthContext updateProfile START ===
Profile data received: {firstName: "Johnny"}
Profile data type: object
Profile data keys: ["firstName"]
Current token exists: true
Current user: {id: "...", email: "..."}
Calling authAPI.updateProfile with: {firstName: "<PERSON>"}
API response received: {...}
Profile update successful, updating state...
=== AuthContext updateProfile SUCCESS ===
    </pre>
    
    <h2>Expected logs in server console:</h2>
    <pre>
Update request data: {firstName: "Johnny"}
Filtered update data: {firstName: "Johnny"}
    </pre>
    
    <h2>If you see empty data:</h2>
    <p>Check if the ProfilePage is properly calling updateProfile with the changed data.</p>
</body>
</html>