const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');
const imageUpload = require('../../middleware/upload/imageUpload');

/**
 * Get all products with pagination and filters (Admin view)
 */
const getAllProducts = async (req, res) => {
  try {
    // Parse query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const search = req.query.search;
    const status = req.query.status;
    const category = req.query.category;
    const vendor = req.query.vendor;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    const featured = req.query.featured;

    // Build filter
    const filter = {};

    if (status) {
      filter.status = status;
    }

    if (category) {
      filter.category = category;
    }

    if (vendor) {
      filter.vendor = vendor;
    }

    if (featured !== undefined) {
      filter.featured = featured === 'true';
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort object
    const sort = {};
    if (sortBy === 'price') {
      sort['pricing.basePrice'] = sortOrder;
    } else if (sortBy === 'sales') {
      sort['sales.totalSold'] = sortOrder;
    } else if (sortBy === 'rating') {
      sort['reviews.averageRating'] = sortOrder;
    } else if (sortBy === 'vendor') {
      sort['vendor.businessName'] = sortOrder;
    } else {
      sort[sortBy] = sortOrder;
    }

    // Get products with pagination
    const [products, totalProducts] = await Promise.all([
      Product.find(filter)
        .populate('vendor', 'businessName user')
        .populate('category', 'name slug')
        .populate('subcategory', 'name slug')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalProducts / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get all products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single product by ID (Admin view)
 */
const getProduct = async (req, res) => {
  try {
    const productId = req.params.id;

    const product = await Product.findById(productId)
      .populate('vendor', 'businessName user contactInfo')
      .populate('category', 'name slug')
      .populate('subcategory', 'name slug')
      .populate('modifiedBy', 'firstName lastName email');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: {
        product
      }
    });

  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update product status (Admin action)
 */
const updateProductStatus = async (req, res) => {
  try {
    const productId = req.params.id;
    const { status, reason } = req.body;

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Update product status
    product.status = status;
    product.lastModified = new Date();
    product.modifiedBy = req.user.userId;

    // Add admin note if reason provided
    if (reason) {
      if (!product.adminNotes) {
        product.adminNotes = [];
      }
      product.adminNotes.push({
        note: reason,
        action: `Status changed to ${status}`,
        addedBy: req.user.userId,
        addedAt: new Date()
      });
    }

    await product.save();

    // Populate for response
    await product.populate([
      { path: 'vendor', select: 'businessName' },
      { path: 'category', select: 'name slug' }
    ]);

    res.json({
      success: true,
      message: 'Product status updated successfully',
      data: {
        product
      }
    });

  } catch (error) {
    console.error('Update product status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update product status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Toggle product featured status (Admin action)
 */
const toggleFeatured = async (req, res) => {
  try {
    const productId = req.params.id;

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Toggle featured status
    product.featured = !product.featured;
    product.lastModified = new Date();
    product.modifiedBy = req.user.userId;

    await product.save();

    res.json({
      success: true,
      message: `Product ${product.featured ? 'featured' : 'unfeatured'} successfully`,
      data: {
        product: {
          _id: product._id,
          name: product.name,
          featured: product.featured
        }
      }
    });

  } catch (error) {
    console.error('Toggle featured error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle featured status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Bulk operations on products (Admin)
 */
const bulkOperation = async (req, res) => {
  try {
    const { productIds, action, reason } = req.body;

    // Verify all products exist
    const products = await Product.find({
      _id: { $in: productIds }
    });

    if (products.length !== productIds.length) {
      return res.status(400).json({
        success: false,
        message: 'Some products not found'
      });
    }

    let updateData = {};
    let message = '';

    switch (action) {
      case 'approve':
        updateData = { status: 'active' };
        message = 'Products approved successfully';
        break;
      case 'reject':
        updateData = { status: 'inactive' };
        message = 'Products rejected successfully';
        break;
      case 'feature':
        updateData = { featured: true };
        message = 'Products featured successfully';
        break;
      case 'unfeature':
        updateData = { featured: false };
        message = 'Products unfeatured successfully';
        break;
      case 'archive':
        updateData = { status: 'archived' };
        message = 'Products archived successfully';
        break;
      case 'delete':
        // Check if any product has orders
        const Order = require('../../models/Order');
        const hasOrders = await Order.findOne({
          'items.product': { $in: productIds }
        });

        if (hasOrders) {
          return res.status(400).json({
            success: false,
            message: 'Cannot delete products with existing orders. Use archive instead.'
          });
        }

        // Delete product images
        for (const product of products) {
          for (const image of product.images) {
            try {
              await imageUpload.deleteImage(image.url);
            } catch (deleteError) {
              console.error('Error deleting image:', deleteError);
            }
          }
        }

        await Product.deleteMany({ _id: { $in: productIds } });
        message = 'Products deleted successfully';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    if (action !== 'delete') {
      // Add admin notes if reason provided
      const adminNote = reason ? {
        note: reason,
        action: `Bulk ${action}`,
        addedBy: req.user.userId,
        addedAt: new Date()
      } : null;

      const updateQuery = {
        ...updateData,
        lastModified: new Date(),
        modifiedBy: req.user.userId
      };

      if (adminNote) {
        updateQuery.$push = { adminNotes: adminNote };
      }

      await Product.updateMany(
        { _id: { $in: productIds } },
        updateQuery
      );
    }

    res.json({
      success: true,
      message,
      data: {
        affectedProducts: products.length
      }
    });

  } catch (error) {
    console.error('Bulk operation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk operation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get product statistics for admin dashboard
 */
const getProductStats = async (req, res) => {
  try {
    // Get overall product statistics
    const stats = await Product.aggregate([
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          activeProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          draftProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          inactiveProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
          },
          archivedProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'archived'] }, 1, 0] }
          },
          featuredProducts: {
            $sum: { $cond: ['$featured', 1, 0] }
          },
          outOfStockProducts: {
            $sum: { $cond: [{ $eq: ['$inventory.stockStatus', 'out_of_stock'] }, 1, 0] }
          },
          lowStockProducts: {
            $sum: { $cond: [{ $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }, 1, 0] }
          },
          totalRevenue: { $sum: '$sales.totalRevenue' },
          totalSold: { $sum: '$sales.totalSold' },
          averagePrice: { $avg: '$pricing.basePrice' },
          averageRating: { $avg: '$reviews.averageRating' }
        }
      }
    ]);

    const productStats = stats[0] || {
      totalProducts: 0,
      activeProducts: 0,
      draftProducts: 0,
      inactiveProducts: 0,
      archivedProducts: 0,
      featuredProducts: 0,
      outOfStockProducts: 0,
      lowStockProducts: 0,
      totalRevenue: 0,
      totalSold: 0,
      averagePrice: 0,
      averageRating: 0
    };

    // Get products by category
    const categoryStats = await Product.aggregate([
      { $match: { status: 'active' } },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      },
      { $unwind: '$categoryInfo' },
      {
        $group: {
          _id: '$category',
          categoryName: { $first: '$categoryInfo.name' },
          productCount: { $sum: 1 },
          totalRevenue: { $sum: '$sales.totalRevenue' }
        }
      },
      { $sort: { productCount: -1 } },
      { $limit: 10 }
    ]);

    // Get top selling products
    const topSellingProducts = await Product.find({ status: 'active' })
      .populate('vendor', 'businessName')
      .populate('category', 'name')
      .sort({ 'sales.totalSold': -1 })
      .limit(10)
      .select('name sales vendor category pricing.basePrice')
      .lean();

    // Get recent products
    const recentProducts = await Product.find()
      .populate('vendor', 'businessName')
      .populate('category', 'name')
      .sort({ createdAt: -1 })
      .limit(10)
      .select('name status vendor category createdAt')
      .lean();

    // Get products needing review (draft status)
    const pendingReview = await Product.countDocuments({ status: 'draft' });

    res.json({
      success: true,
      data: {
        stats: productStats,
        categoryStats,
        topSellingProducts,
        recentProducts,
        pendingReview
      }
    });

  } catch (error) {
    console.error('Get product stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get low stock products
 */
const getLowStockProducts = async (req, res) => {
  try {
    const lowStockProducts = await Product.find({
      'inventory.trackQuantity': true,
      $expr: { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }
    })
      .populate('vendor', 'businessName contactInfo.businessEmail')
      .populate('category', 'name')
      .select('name sku inventory vendor category')
      .sort({ 'inventory.quantity': 1 })
      .lean();

    res.json({
      success: true,
      data: {
        products: lowStockProducts,
        count: lowStockProducts.length
      }
    });

  } catch (error) {
    console.error('Get low stock products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch low stock products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get products by vendor
 */
const getProductsByVendor = async (req, res) => {
  try {
    const vendorId = req.params.vendorId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Verify vendor exists
    const vendor = await Vendor.findById(vendorId);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const [products, totalProducts] = await Promise.all([
      Product.find({ vendor: vendorId })
        .populate('category', 'name slug')
        .populate('subcategory', 'name slug')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments({ vendor: vendorId })
    ]);

    const totalPages = Math.ceil(totalProducts / limit);

    res.json({
      success: true,
      data: {
        vendor: {
          _id: vendor._id,
          businessName: vendor.businessName
        },
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get products by vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendor products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get products pending approval
 */
const getPendingApproval = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const products = await Product.find({ 'approval.status': 'pending' })
      .populate('vendor', 'businessName user')
      .populate('category', 'name slug')
      .populate('subcategory', 'name slug')
      .sort({ 'approval.submittedAt': 1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const totalProducts = await Product.countDocuments({ 'approval.status': 'pending' });
    const totalPages = Math.ceil(totalProducts / limit);

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get pending approval products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending approval products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Approve product
 */
const approveProduct = async (req, res) => {
  try {
    const productId = req.params.id;
    const { notes } = req.body;
    const adminId = req.user.userId;

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    if (product.approval.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Product is not pending approval'
      });
    }

    await product.approveProduct(adminId, notes);

    // TODO: Send notification to vendor

    res.json({
      success: true,
      message: 'Product approved successfully',
      data: { product }
    });

  } catch (error) {
    console.error('Approve product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Reject product
 */
const rejectProduct = async (req, res) => {
  try {
    const productId = req.params.id;
    const { reason, notes } = req.body;
    const adminId = req.user.userId;

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    if (product.approval.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Product is not pending approval'
      });
    }

    await product.rejectProduct(adminId, reason, notes);

    // TODO: Send notification to vendor

    res.json({
      success: true,
      message: 'Product rejected successfully',
      data: { product }
    });

  } catch (error) {
    console.error('Reject product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Request changes for product
 */
const requestProductChanges = async (req, res) => {
  try {
    const productId = req.params.id;
    const { changes, notes } = req.body;
    const adminId = req.user.userId;

    if (!changes || !Array.isArray(changes) || changes.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Changes array is required'
      });
    }

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    if (product.approval.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Product is not pending approval'
      });
    }

    await product.requestChanges(adminId, changes, notes);

    // TODO: Send notification to vendor

    res.json({
      success: true,
      message: 'Changes requested successfully',
      data: { product }
    });

  } catch (error) {
    console.error('Request product changes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to request changes',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAllProducts,
  getProduct,
  updateProductStatus,
  toggleFeatured,
  bulkOperation,
  getProductStats,
  getLowStockProducts,
  getProductsByVendor,
  getPendingApproval,
  approveProduct,
  rejectProduct,
  requestProductChanges
};