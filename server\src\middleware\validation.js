const { validationResult } = require('express-validator');

// Middleware to handle validation errors
const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => ({
            field: error.path || error.param,
            message: error.msg,
            value: error.value
        }));

        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errorMessages
        });
    }
    
    next();
};

// Custom validation functions
const customValidators = {
    // Check if password is strong
    isStrongPassword: (value) => {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        const hasNumbers = /\d/.test(value);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
        
        return value.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers;
    },

    // Check if phone number is valid international format
    isValidPhoneNumber: (value) => {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(value.replace(/\s/g, ''));
    },

    // Check if business name is valid
    isValidBusinessName: (value) => {
        const businessNameRegex = /^[a-zA-Z0-9\s\-\&\.\,\'\"]+$/;
        return businessNameRegex.test(value) && value.length >= 2 && value.length <= 100;
    },

    // Check if name contains only valid characters
    isValidName: (value) => {
        const nameRegex = /^[a-zA-Z\s\-\'\.]+$/;
        return nameRegex.test(value) && value.length >= 1 && value.length <= 50;
    },

    // Check if email domain is not disposable
    isNotDisposableEmail: (value) => {
        const disposableDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'throwaway.email'
        ];
        
        const domain = value.split('@')[1];
        return !disposableDomains.includes(domain.toLowerCase());
    }
};

// Sanitization functions
const sanitizers = {
    // Sanitize name fields
    sanitizeName: (value) => {
        return value.trim().replace(/\s+/g, ' ');
    },

    // Sanitize business name
    sanitizeBusinessName: (value) => {
        return value.trim().replace(/\s+/g, ' ');
    },

    // Sanitize phone number
    sanitizePhoneNumber: (value) => {
        return value.replace(/\D/g, '');
    }
};

// Common validation chains
const commonValidations = {
    email: [
        'email',
        'normalizeEmail',
        { custom: customValidators.isNotDisposableEmail }
    ],
    
    password: [
        { isLength: { min: 8 } },
        { custom: customValidators.isStrongPassword }
    ],
    
    name: [
        'trim',
        { isLength: { min: 1, max: 50 } },
        { custom: customValidators.isValidName }
    ],
    
    businessName: [
        'trim',
        { isLength: { min: 2, max: 100 } },
        { custom: customValidators.isValidBusinessName }
    ],
    
    phone: [
        'trim',
        { custom: customValidators.isValidPhoneNumber }
    ]
};

module.exports = {
    validateRequest,
    customValidators,
    sanitizers,
    commonValidations
};