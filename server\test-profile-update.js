/**
 * Comprehensive test script for profile update endpoint
 * Run with: node test-profile-update.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test scenarios for profile updates
const testScenarios = [
    {
        name: 'Valid User Profile Update',
        userType: 'user',
        data: {
            firstName: 'John',
            lastName: 'Doe',
            address: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'United States'
        },
        shouldSucceed: true
    },
    {
        name: 'Valid Vendor Profile Update',
        userType: 'vendor',
        data: {
            businessName: 'Acme Corporation',
            businessType: 'retail',
            contactPerson: '<PERSON>',
            address: '456 Business Ave',
            city: 'Los Angeles',
            state: 'CA',
            zipCode: '90210',
            country: 'United States'
        },
        shouldSucceed: true
    },
    {
        name: 'Update with Preferences',
        userType: 'user',
        data: {
            firstName: 'Alice',
            preferences: {
                language: 'en',
                currency: 'USD',
                timezone: 'America/New_York'
            }
        },
        shouldSucceed: true
    },
    {
        name: 'Invalid Business Type',
        userType: 'vendor',
        data: {
            businessType: 'invalid_type'
        },
        shouldSucceed: false
    },
    {
        name: 'Invalid Gender',
        userType: 'user',
        data: {
            gender: 'invalid_gender'
        },
        shouldSucceed: false
    },
    {
        name: 'Too Long First Name',
        userType: 'user',
        data: {
            firstName: 'A'.repeat(51) // Exceeds 50 character limit
        },
        shouldSucceed: false
    },
    {
        name: 'Empty Update Data',
        userType: 'user',
        data: {},
        shouldSucceed: false
    },
    {
        name: 'Invalid Date Format',
        userType: 'user',
        data: {
            dateOfBirth: 'invalid-date'
        },
        shouldSucceed: false
    }
];

async function createTestUser(userType = 'user') {
    try {
        const timestamp = Date.now();
        const userData = {
            email: `test.${userType}.${timestamp}@example.com`,
            password: 'TestPassword123',
            userType
        };

        if (userType === 'user') {
            userData.firstName = 'Test';
            userData.lastName = 'User';
        } else if (userType === 'vendor') {
            userData.businessName = 'Test Business';
            userData.businessType = 'retail';
            userData.contactPerson = 'Test Contact';
        }

        console.log(`📝 Creating test ${userType}...`);
        const response = await axios.post(`${BASE_URL}/auth/register`, userData);
        
        if (response.data.success) {
            console.log(`✅ Test ${userType} created: ${userData.email}`);
            return {
                token: response.data.data.token,
                user: response.data.data.user,
                email: userData.email
            };
        } else {
            console.log(`❌ Failed to create test ${userType}`);
            return null;
        }
    } catch (error) {
        if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
            console.log(`⚠️ Test ${userType} already exists, trying to login...`);
            // Try to login with existing user
            try {
                const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
                    email: `test.${userType}.${timestamp}@example.com`,
                    password: 'TestPassword123'
                });
                return {
                    token: loginResponse.data.data.token,
                    user: loginResponse.data.data.user,
                    email: `test.${userType}.${timestamp}@example.com`
                };
            } catch (loginError) {
                console.log(`❌ Failed to login with existing ${userType}`);
                return null;
            }
        } else {
            console.log(`❌ Error creating test ${userType}:`, error.response?.data?.message || error.message);
            return null;
        }
    }
}

async function testProfileUpdate(token, updateData, scenarioName) {
    try {
        console.log(`\n🧪 Testing: ${scenarioName}`);
        console.log(`   Update data:`, JSON.stringify(updateData, null, 2));

        const response = await axios.put(`${BASE_URL}/auth/profile`, updateData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log(`   ✅ Success: ${response.status} - ${response.data.message}`);
        if (response.data.data?.user) {
            console.log(`   📧 User email: ${response.data.data.user.email}`);
            console.log(`   👤 User type: ${response.data.data.user.userType}`);
        }
        return { success: true, data: response.data };

    } catch (error) {
        if (error.response) {
            console.log(`   ❌ Error: ${error.response.status} - ${error.response.data.message}`);
            if (error.response.data.errors) {
                console.log(`   📝 Validation errors:`);
                error.response.data.errors.forEach(err => {
                    console.log(`      - ${err.field}: ${err.message}`);
                });
            }
            return { success: false, error: error.response.data };
        } else {
            console.log(`   ❌ Network error: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

async function testGetProfile(token, userEmail) {
    try {
        console.log(`\n📋 Getting profile for ${userEmail}...`);
        const response = await axios.get(`${BASE_URL}/auth/profile`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log(`   ✅ Profile retrieved successfully`);
        console.log(`   📧 Email: ${response.data.data.user.email}`);
        console.log(`   👤 Type: ${response.data.data.user.userType}`);
        console.log(`   ✉️ Verified: ${response.data.data.user.emailVerification.isVerified}`);
        
        return response.data.data.user;
    } catch (error) {
        console.log(`   ❌ Error getting profile: ${error.response?.data?.message || error.message}`);
        return null;
    }
}

async function testAuthenticationIssues() {
    console.log('\n🔐 Testing Authentication Issues...\n');

    // Test without token
    console.log('1. Testing without authentication token...');
    try {
        await axios.put(`${BASE_URL}/auth/profile`, { firstName: 'Test' });
        console.log('   ❌ Should have failed without token');
    } catch (error) {
        console.log(`   ✅ Correctly rejected: ${error.response.status} - ${error.response.data.message}`);
    }

    // Test with invalid token
    console.log('\n2. Testing with invalid token...');
    try {
        await axios.put(`${BASE_URL}/auth/profile`, { firstName: 'Test' }, {
            headers: { 'Authorization': 'Bearer invalid_token_12345' }
        });
        console.log('   ❌ Should have failed with invalid token');
    } catch (error) {
        console.log(`   ✅ Correctly rejected: ${error.response.status} - ${error.response.data.message}`);
    }

    // Test with malformed token
    console.log('\n3. Testing with malformed authorization header...');
    try {
        await axios.put(`${BASE_URL}/auth/profile`, { firstName: 'Test' }, {
            headers: { 'Authorization': 'InvalidFormat token123' }
        });
        console.log('   ❌ Should have failed with malformed header');
    } catch (error) {
        console.log(`   ✅ Correctly rejected: ${error.response.status} - ${error.response.data.message}`);
    }
}

async function runComprehensiveTests() {
    console.log('🚀 Starting Comprehensive Profile Update Tests...\n');

    // Check server health
    try {
        await axios.get(`${BASE_URL}/auth/health`);
        console.log('✅ Server is running\n');
    } catch (error) {
        console.log('❌ Server is not running. Please start with: npm run dev');
        return;
    }

    // Test authentication issues first
    await testAuthenticationIssues();

    // Create test users
    console.log('\n👥 Creating Test Users...\n');
    const testUser = await createTestUser('user');
    const testVendor = await createTestUser('vendor');

    if (!testUser || !testVendor) {
        console.log('❌ Failed to create test users. Cannot proceed with profile tests.');
        return;
    }

    // Test getting profiles
    await testGetProfile(testUser.token, testUser.email);
    await testGetProfile(testVendor.token, testVendor.email);

    // Run profile update tests
    console.log('\n🔄 Running Profile Update Tests...\n');
    const results = [];

    for (const scenario of testScenarios) {
        const token = scenario.userType === 'user' ? testUser.token : testVendor.token;
        const result = await testProfileUpdate(token, scenario.data, scenario.name);
        
        results.push({
            scenario: scenario.name,
            expected: scenario.shouldSucceed,
            actual: result.success,
            passed: scenario.shouldSucceed === result.success
        });

        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Summary
    console.log('\n📊 Test Results Summary:\n');
    const passed = results.filter(r => r.passed).length;
    const total = results.length;

    console.log(`✅ Passed: ${passed}/${total} tests`);
    console.log(`❌ Failed: ${total - passed}/${total} tests\n`);

    if (passed !== total) {
        console.log('❌ Failed tests:');
        results.filter(r => !r.passed).forEach(test => {
            console.log(`   - ${test.scenario}: Expected ${test.expected ? 'SUCCESS' : 'FAILURE'}, got ${test.actual ? 'SUCCESS' : 'FAILURE'}`);
        });
    }

    console.log('\n💡 Common Issues and Solutions:');
    console.log('   1. Missing Authorization header → Add "Bearer <token>"');
    console.log('   2. Invalid token → Login again to get fresh token');
    console.log('   3. Empty update data → Include at least one valid field');
    console.log('   4. Validation errors → Check field formats and limits');
    console.log('   5. User not found → Ensure user exists and token is valid');
}

// Main execution
runComprehensiveTests().catch(error => {
    console.error('\n❌ Test script error:', error);
    process.exit(1);
});