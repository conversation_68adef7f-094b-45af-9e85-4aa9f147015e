const axios = require('axios');

async function testPublicRoutes() {
  try {
    console.log('🧪 Testing Public Routes...');
    
    const baseUrl = 'http://localhost:5000/api';
    
    // Test 1: Test route
    try {
      console.log('\n📡 Testing: /api/public/test');
      const testResponse = await axios.get(`${baseUrl}/public/test`);
      console.log('✅ Test route works:', testResponse.data);
    } catch (error) {
      console.log('❌ Test route failed:', error.response?.status, error.response?.data);
    }
    
    // Test 2: Categories route
    try {
      console.log('\n📡 Testing: /api/public/categories');
      const categoriesResponse = await axios.get(`${baseUrl}/public/categories`);
      console.log('✅ Categories route works! Count:', categoriesResponse.data.data.categories.length);
    } catch (error) {
      console.log('❌ Categories route failed:', error.response?.status, error.response?.data);
    }
    
    // Test 3: Check if routes are mounted at root
    try {
      console.log('\n📡 Testing: /api/categories (without public prefix)');
      const rootCategoriesResponse = await axios.get(`${baseUrl}/categories`);
      console.log('✅ Root categories route works! Count:', rootCategoriesResponse.data.data.categories.length);
    } catch (error) {
      console.log('❌ Root categories route failed:', error.response?.status, error.response?.data);
    }
    
  } catch (error) {
    console.error('❌ General error:', error.message);
  }
}

testPublicRoutes();