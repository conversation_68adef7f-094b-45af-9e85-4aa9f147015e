const express = require('express');
const { body, query } = require('express-validator');
const multer = require('multer');
const authController = require('../controllers/authController');
const { 
    verifyToken, 
    requireEmailVerification, 
    authRateLimit 
} = require('../middleware/auth/authMiddleware');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Configure multer for profile updates
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
        fieldSize: 1024 * 1024 // 1MB field size limit
    },
    fileFilter: (req, file, cb) => {
        // Allow common image formats for profile pictures
        const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'), false);
        }
    }
});

// Middleware to handle different content types for profile updates
const handleProfileContentType = (req, res, next) => {
    const contentType = req.headers['content-type'] || '';
    
    if (contentType.includes('multipart/form-data')) {
        // Use multer for multipart data
        upload.any()(req, res, next);
    } else {
        // For JSON or URL-encoded data, continue without multer
        next();
    }
};

// Validation rules
const registerValidation = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    body('userType')
        .isIn(['user', 'vendor'])
        .withMessage('User type must be either user or vendor'),
    
    // User-specific validations
    body('firstName')
        .if(body('userType').equals('user'))
        .notEmpty()
        .withMessage('First name is required for user accounts')
        .isLength({ max: 50 })
        .withMessage('First name must not exceed 50 characters'),
    body('lastName')
        .if(body('userType').equals('user'))
        .notEmpty()
        .withMessage('Last name is required for user accounts')
        .isLength({ max: 50 })
        .withMessage('Last name must not exceed 50 characters'),
    
    // Vendor-specific validations
    body('businessName')
        .if(body('userType').equals('vendor'))
        .notEmpty()
        .withMessage('Business name is required for vendor accounts')
        .isLength({ max: 100 })
        .withMessage('Business name must not exceed 100 characters'),
    body('businessType')
        .if(body('userType').equals('vendor'))
        .isIn(['retail', 'wholesale', 'manufacturer', 'dropshipping', 'services', 'other'])
        .withMessage('Please select a valid business type'),
    body('contactPerson')
        .if(body('userType').equals('vendor'))
        .notEmpty()
        .withMessage('Contact person is required for vendor accounts')
        .isLength({ max: 100 })
        .withMessage('Contact person name must not exceed 100 characters')
];

const updateProfileValidation = [
    body('firstName')
        .optional()
        .isLength({ max: 50 })
        .withMessage('First name must not exceed 50 characters'),
    body('lastName')
        .optional()
        .isLength({ max: 50 })
        .withMessage('Last name must not exceed 50 characters'),
    body('businessName')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Business name must not exceed 100 characters'),
    body('businessType')
        .optional()
        .isIn(['retail', 'wholesale', 'manufacturer', 'dropshipping', 'services', 'other'])
        .withMessage('Please select a valid business type'),
    body('contactPerson')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Contact person name must not exceed 100 characters'),
    body('dateOfBirth')
        .optional()
        .isISO8601()
        .withMessage('Please provide a valid date'),
    body('gender')
        .optional()
        .isIn(['male', 'female', 'other', 'prefer_not_to_say'])
        .withMessage('Please select a valid gender'),
    body('address')
        .optional()
        .isLength({ max: 200 })
        .withMessage('Address must not exceed 200 characters'),
    body('city')
        .optional()
        .isLength({ max: 100 })
        .withMessage('City must not exceed 100 characters'),
    body('state')
        .optional()
        .isLength({ max: 100 })
        .withMessage('State must not exceed 100 characters'),
    body('zipCode')
        .optional()
        .isLength({ max: 20 })
        .withMessage('ZIP code must not exceed 20 characters'),
    body('country')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Country must not exceed 100 characters'),
    // Preferences validation - handle nested object structure
    // Note: We handle detailed preference validation in the controller for better UX
    body('preferences')
        .optional()
        .isObject()
        .withMessage('Preferences must be an object')
];

const loginValidation = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .notEmpty()
        .withMessage('Password is required')
];

const forgotPasswordValidation = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email')
];

const resetPasswordValidation = [
    body('token')
        .notEmpty()
        .withMessage('Reset token is required'),
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('newPassword')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number')
];

const changePasswordValidation = [
    body('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    body('newPassword')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number')
];

const verifyEmailValidation = [
    query('token')
        .notEmpty()
        .withMessage('Verification token is required'),
    query('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email')
];

// Debug middleware for registration
const debugRegistration = (req, res, next) => {
    console.log('=== INCOMING REGISTRATION REQUEST ===');
    console.log('Method:', req.method);
    console.log('URL:', req.url);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Body:', JSON.stringify(req.body, null, 2));
    console.log('Raw body type:', typeof req.body);
    console.log('Body keys:', Object.keys(req.body || {}));
    console.log('Content-Length:', req.headers['content-length']);
    console.log('==========================================');
    next();
};

// Debug middleware for profile updates
const debugProfileUpdate = (req, res, next) => {
    console.log('=== PROFILE UPDATE DEBUG ===');
    console.log('- User ID:', req.user?.userId);
    console.log('- Request body:', JSON.stringify(req.body, null, 2));
    console.log('- Update data:', JSON.stringify(req.body || {}, null, 2));
    console.log('- Content-Type:', req.headers['content-type']);
    console.log('- Request method:', req.method);
    console.log('- Files:', req.files);
    console.log('==========================================');
    next();
};

// Public routes (no authentication required)

// Register new user
router.post('/register', 
    debugRegistration,
    // authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes - TEMPORARILY DISABLED FOR DEBUGGING
    registerValidation,
    validateRequest,
    authController.register
);

// Login user
router.post('/login',
    authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
    loginValidation,
    validateRequest,
    authController.login
);

// Verify email
router.get('/verify-email',
    verifyEmailValidation,
    validateRequest,
    authController.verifyEmail
);

// Resend verification email
router.post('/resend-verification',
    authRateLimit(3, 5 * 60 * 1000), // 3 attempts per 5 minutes
    body('email').isEmail().normalizeEmail(),
    validateRequest,
    authController.resendVerificationEmail
);

// Forgot password
router.post('/forgot-password',
    authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
    forgotPasswordValidation,
    validateRequest,
    authController.forgotPassword
);

// Reset password
router.post('/reset-password',
    authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
    resetPasswordValidation,
    validateRequest,
    authController.resetPassword
);

// Refresh token
router.post('/refresh-token',
    body('refreshToken').notEmpty().withMessage('Refresh token is required'),
    validateRequest,
    authController.refreshToken
);

// Protected routes (authentication required)

// Get current user profile
router.get('/profile',
    verifyToken,
    authController.getProfile
);

// Update user profile - FIXED: Handle both multipart and JSON data
router.put('/profile',
    verifyToken,
    handleProfileContentType, // Handle different content types
    debugProfileUpdate,
    updateProfileValidation,
    validateRequest,
    authController.updateProfile
);

// Change password
router.put('/change-password',
    verifyToken,
    changePasswordValidation,
    validateRequest,
    authController.changePassword
);

// Logout
router.post('/logout',
    verifyToken,
    authController.logout
);

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Auth service is running',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;