const express = require('express');
const router = express.Router();

// Import public route modules
const productRoutes = require('./products');
const categoryRoutes = require('./categories');
const searchRoutes = require('./search');
const testRoutes = require('./test');

// Test route first
router.use('/test', testRoutes);

// Mount routes
router.use('/products', productRoutes);
router.use('/categories', categoryRoutes);
router.use('/search', searchRoutes);

module.exports = router;