#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');

const DB_URL = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/multi-vendor-ecommerce';

async function testConnection() {
    console.log('🔧 Testing MongoDB Connection\n');
    console.log('📍 Connecting to:', DB_URL.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@'));
    
    try {
        // Set global mongoose options before connecting
        mongoose.set('bufferCommands', false);
        mongoose.set('strictQuery', false);
        
        await mongoose.connect(DB_URL, {
            serverSelectionTimeoutMS: 10000,
            socketTimeoutMS: 45000,
            maxPoolSize: 10,
            minPoolSize: 5,
            maxIdleTimeMS: 30000,
            family: 4, // Use IPv4, skip trying IPv6
        });
        
        console.log('✅ Database connection successful!');
        
        // Test basic operations
        console.log('\n🔍 Testing basic database operations...');
        
        // List databases
        const admin = mongoose.connection.db.admin();
        const dbs = await admin.listDatabases();
        console.log('📋 Available databases:', dbs.databases.map(db => db.name).join(', '));
        
        // Test collection access
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('📁 Collections in current database:', collections.map(col => col.name).join(', ') || 'None (new database)');
        
        console.log('\n✅ All tests passed! Your database is ready.');
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        
        if (error.message.includes('ECONNREFUSED')) {
            console.log('\n💡 Troubleshooting:');
            console.log('   1. Make sure MongoDB is installed and running');
            console.log('   2. Run: npm run setup:mongodb');
            console.log('   3. Or start MongoDB manually: mongod');
            console.log('   4. Check if MongoDB service is running on port 27017');
        } else if (error.message.includes('authentication failed')) {
            console.log('\n💡 Authentication issue:');
            console.log('   1. Check your MongoDB credentials');
            console.log('   2. For local development, use: mongodb://127.0.0.1:27017/multi-vendor-ecommerce');
        } else if (error.message.includes('timeout')) {
            console.log('\n💡 Connection timeout:');
            console.log('   1. Check your internet connection (for Atlas)');
            console.log('   2. Verify MongoDB Atlas IP whitelist');
            console.log('   3. Try using local MongoDB instead');
        } else if (error.message.includes('not supported') || error.message.includes('not a valid option')) {
            console.log('\n💡 Configuration issue:');
            console.log('   1. Mongoose version compatibility issue resolved');
            console.log('   2. Using modern Mongoose 8.x configuration');
        }
    } finally {
        await mongoose.connection.close();
        console.log('\n📴 Connection closed');
        process.exit(0);
    }
}

testConnection();