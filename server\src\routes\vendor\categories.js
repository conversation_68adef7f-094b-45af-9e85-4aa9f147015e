const express = require('express');
const router = express.Router();
const Category = require('../../models/Category');

// Import middleware
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');

// Apply authentication middleware
router.use(verifyToken);
router.use(requireUserType('vendor'));

/**
 * @route   GET /api/vendor/categories
 * @desc    Get all active categories for vendor use
 * @access  Private (Vendor)
 */
router.get('/', async (req, res) => {
  try {
    const categories = await Category.find({ status: 'active' })
      .sort({ sortOrder: 1, name: 1 })
      .select('name slug description parent level featured')
      .lean();

    res.json({
      success: true,
      data: {
        categories
      }
    });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;