const express = require('express');
const app = express();

// Import the routes to see what's available
const routes = require('./src/routes');

// Add a debug endpoint
app.get('/debug/routes', (req, res) => {
  const routes = [];
  
  function extractRoutes(stack, prefix = '') {
    stack.forEach(layer => {
      if (layer.route) {
        // Regular route
        const methods = Object.keys(layer.route.methods);
        routes.push({
          path: prefix + layer.route.path,
          methods: methods
        });
      } else if (layer.name === 'router') {
        // Nested router
        const routerPrefix = layer.regexp.source
          .replace('\\', '')
          .replace('(?=\\/|$)', '')
          .replace('^', '')
          .replace('$', '');
        
        if (layer.handle.stack) {
          extractRoutes(layer.handle.stack, prefix + routerPrefix);
        }
      }
    });
  }
  
  extractRoutes(app._router.stack);
  
  res.json({
    success: true,
    routes: routes,
    totalRoutes: routes.length
  });
});

// Mount the main routes
app.use('/api', routes);

// Start server
const PORT = 3001; // Different port to avoid conflict
app.listen(PORT, () => {
  console.log(`Debug server running on port ${PORT}`);
  console.log(`Visit http://localhost:${PORT}/debug/routes to see all routes`);
});