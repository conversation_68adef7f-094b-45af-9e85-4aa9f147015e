const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');

/**
 * Get all active products for public viewing
 */
const getProducts = async (req, res) => {
  try {
    // Parse query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;
    const search = req.query.search;
    const category = req.query.category;
    const subcategory = req.query.subcategory;
    const vendor = req.query.vendor;
    const minPrice = parseFloat(req.query.minPrice);
    const maxPrice = parseFloat(req.query.maxPrice);
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    const featured = req.query.featured;
    const inStock = req.query.inStock;

    // Build filter for active products only
    const filter = { 
      status: 'active',
      visibility: 'public'
    };

    if (category) {
      filter.category = category;
    }

    if (subcategory) {
      filter.subcategory = subcategory;
    }

    if (vendor) {
      filter.vendor = vendor;
    }

    if (featured === 'true') {
      filter.featured = true;
    }

    if (inStock === 'true') {
      filter.$or = [
        { 'inventory.trackQuantity': false },
        { 
          'inventory.trackQuantity': true,
          'inventory.quantity': { $gt: 0 }
        },
        {
          'inventory.trackQuantity': true,
          'inventory.allowBackorders': true
        }
      ];
    }

    // Price range filter
    if (!isNaN(minPrice) || !isNaN(maxPrice)) {
      filter['pricing.basePrice'] = {};
      if (!isNaN(minPrice)) {
        filter['pricing.basePrice'].$gte = minPrice;
      }
      if (!isNaN(maxPrice)) {
        filter['pricing.basePrice'].$lte = maxPrice;
      }
    }

    // Search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort object
    const sort = {};
    if (sortBy === 'price') {
      sort['pricing.basePrice'] = sortOrder;
    } else if (sortBy === 'rating') {
      sort['reviews.averageRating'] = sortOrder;
    } else if (sortBy === 'popularity') {
      sort['sales.totalSold'] = sortOrder;
    } else if (sortBy === 'name') {
      sort.name = sortOrder;
    } else {
      sort[sortBy] = sortOrder;
    }

    // Get products with pagination
    const [products, totalProducts] = await Promise.all([
      Product.find(filter)
        .populate('vendor', 'businessName')
        .populate('category', 'name slug')
        .populate('subcategory', 'name slug')
        .select('-__v -modifiedBy -lastModified')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalProducts / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single product by ID or slug
 */
const getProduct = async (req, res) => {
  try {
    const identifier = req.params.id;
    let product;

    // Try to find by ID first, then by slug
    if (identifier.match(/^[0-9a-fA-F]{24}$/)) {
      product = await Product.findOne({ 
        _id: identifier, 
        status: 'active',
        visibility: 'public'
      });
    } else {
      product = await Product.findOne({ 
        slug: identifier, 
        status: 'active',
        visibility: 'public'
      });
    }

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Populate related data
    await product.populate([
      { 
        path: 'vendor', 
        select: 'businessName businessDescription logo contactInfo.website performance.rating performance.totalReviews'
      },
      { path: 'category', select: 'name slug' },
      { path: 'subcategory', select: 'name slug' }
    ]);

    // Get related products from same category
    const relatedProducts = await Product.find({
      category: product.category._id,
      _id: { $ne: product._id },
      status: 'active',
      visibility: 'public'
    })
      .populate('vendor', 'businessName')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
      .limit(8)
      .lean();

    res.json({
      success: true,
      data: {
        product,
        relatedProducts
      }
    });

  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get featured products
 */
const getFeaturedProducts = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;

    const products = await Product.find({
      status: 'active',
      visibility: 'public',
      featured: true
    })
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating sales.totalSold')
      .sort({ 'sales.totalSold': -1, createdAt: -1 })
      .limit(limit)
      .lean();

    res.json({
      success: true,
      data: {
        products
      }
    });

  } catch (error) {
    console.error('Get featured products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch featured products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get new arrivals
 */
const getNewArrivals = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;
    const days = parseInt(req.query.days) || 30;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const products = await Product.find({
      status: 'active',
      visibility: 'public',
      createdAt: { $gte: startDate }
    })
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating createdAt')
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean();

    res.json({
      success: true,
      data: {
        products
      }
    });

  } catch (error) {
    console.error('Get new arrivals error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch new arrivals',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get best selling products
 */
const getBestSelling = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;

    const products = await Product.find({
      status: 'active',
      visibility: 'public',
      'sales.totalSold': { $gt: 0 }
    })
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating sales.totalSold')
      .sort({ 'sales.totalSold': -1 })
      .limit(limit)
      .lean();

    res.json({
      success: true,
      data: {
        products
      }
    });

  } catch (error) {
    console.error('Get best selling products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch best selling products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get products by category
 */
const getProductsByCategory = async (req, res) => {
  try {
    const categoryId = req.params.categoryId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Verify category exists
    const category = await Category.findOne({ 
      _id: categoryId, 
      status: 'active' 
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Get all descendant categories
    const descendants = await category.getDescendants();
    const categoryIds = [categoryId, ...descendants.map(d => d._id)];

    // Build sort object
    const sort = {};
    if (sortBy === 'price') {
      sort['pricing.basePrice'] = sortOrder;
    } else if (sortBy === 'rating') {
      sort['reviews.averageRating'] = sortOrder;
    } else if (sortBy === 'popularity') {
      sort['sales.totalSold'] = sortOrder;
    } else {
      sort[sortBy] = sortOrder;
    }

    const [products, totalProducts] = await Promise.all([
      Product.find({
        category: { $in: categoryIds },
        status: 'active',
        visibility: 'public'
      })
        .populate('vendor', 'businessName')
        .populate('category', 'name slug')
        .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments({
        category: { $in: categoryIds },
        status: 'active',
        visibility: 'public'
      })
    ]);

    const totalPages = Math.ceil(totalProducts / limit);

    res.json({
      success: true,
      data: {
        category: {
          _id: category._id,
          name: category.name,
          slug: category.slug,
          description: category.description
        },
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get products by category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products by category',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get products by vendor
 */
const getProductsByVendor = async (req, res) => {
  try {
    const vendorId = req.params.vendorId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Verify vendor exists and is active
    const vendor = await Vendor.findOne({ 
      _id: vendorId, 
      status: 'active',
      'verification.status': 'verified'
    }).populate('user', 'firstName lastName');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const [products, totalProducts] = await Promise.all([
      Product.find({
        vendor: vendorId,
        status: 'active',
        visibility: 'public'
      })
        .populate('category', 'name slug')
        .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments({
        vendor: vendorId,
        status: 'active',
        visibility: 'public'
      })
    ]);

    const totalPages = Math.ceil(totalProducts / limit);

    res.json({
      success: true,
      data: {
        vendor: {
          _id: vendor._id,
          businessName: vendor.businessName,
          businessDescription: vendor.businessDescription,
          logo: vendor.logo,
          performance: vendor.performance
        },
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get products by vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products by vendor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Search products
 */
const searchProducts = async (req, res) => {
  try {
    const query = req.query.q;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters long'
      });
    }

    // Build search filter
    const searchFilter = {
      status: 'active',
      visibility: 'public',
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { brand: { $regex: query, $options: 'i' } }
      ]
    };

    const [products, totalProducts] = await Promise.all([
      Product.find(searchFilter)
        .populate('vendor', 'businessName')
        .populate('category', 'name slug')
        .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
        .sort({ 'sales.totalSold': -1, 'reviews.averageRating': -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments(searchFilter)
    ]);

    const totalPages = Math.ceil(totalProducts / limit);

    res.json({
      success: true,
      data: {
        query,
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Search products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getProducts,
  getProduct,
  getFeaturedProducts,
  getNewArrivals,
  getBestSelling,
  getProductsByCategory,
  getProductsByVendor,
  searchProducts
};