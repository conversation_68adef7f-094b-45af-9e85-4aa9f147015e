import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Upload,
  Avatar,
  Space,
  Tabs,
  InputNumber
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  UploadOutlined,
  ShopOutlined,
  UserOutlined,
  BellOutlined,
  SecurityScanOutlined,
  CameraOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Settings = () => {
  const [businessForm] = Form.useForm();
  const [profileForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSaveSettings = async (formType, values) => {
    setLoading(true);
    try {
      // Simulate API call
      setTimeout(() => {
        message.success(`${formType} settings saved successfully`);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('Failed to save settings');
      setLoading(false);
    }
  };

  const businessSettings = (
    <Card>
      <Form
        form={businessForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Business', values)}
        initialValues={{
          businessName: 'TechStore Pro',
          businessDescription: 'Leading electronics retailer with premium products and excellent customer service.',
          businessType: 'electronics',
          contactPerson: 'John Smith',
          businessEmail: '<EMAIL>',
          businessPhone: '******-0123',
          website: 'https://techstore.com',
          businessAddress: '123 Business St, Tech City, TC 12345',
          taxId: 'TAX123456789',
          businessLicense: 'LIC987654321',
          returnPolicy: '30-day return policy for all products',
          shippingPolicy: 'Free shipping on orders over $50',
          minimumOrderAmount: 25,
          processingTime: 2,
          isActive: true,
          acceptsReturns: true,
          providesWarranty: true
        }}
      >
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center', marginBottom: 24 }}>
            <Avatar
              size={100}
              icon={<ShopOutlined />}
              style={{ backgroundColor: '#52c41a' }}
            />
            <div style={{ marginTop: 8 }}>
              <Upload
                showUploadList={false}
                beforeUpload={() => false}
              >
                <Button icon={<CameraOutlined />} type="link">
                  Change Logo
                </Button>
              </Upload>
            </div>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="businessName"
              label="Business Name"
              rules={[{ required: true, message: 'Please enter business name' }]}
            >
              <Input placeholder="Enter business name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="businessType"
              label="Business Type"
              rules={[{ required: true, message: 'Please select business type' }]}
            >
              <Select placeholder="Select business type">
                <Option value="electronics">Electronics</Option>
                <Option value="clothing">Clothing & Fashion</Option>
                <Option value="books">Books & Media</Option>
                <Option value="home">Home & Garden</Option>
                <Option value="sports">Sports & Outdoors</Option>
                <Option value="beauty">Beauty & Personal Care</Option>
                <Option value="automotive">Automotive</Option>
                <Option value="other">Other</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="businessDescription"
          label="Business Description"
          rules={[{ required: true, message: 'Please enter business description' }]}
        >
          <TextArea rows={3} placeholder="Describe your business" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="contactPerson"
              label="Contact Person"
              rules={[{ required: true, message: 'Please enter contact person' }]}
            >
              <Input placeholder="Enter contact person name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="businessEmail"
              label="Business Email"
              rules={[
                { required: true, message: 'Please enter business email' },
                { type: 'email', message: 'Please enter valid email' }
              ]}
            >
              <Input placeholder="Enter business email" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="businessPhone"
              label="Business Phone"
              rules={[{ required: true, message: 'Please enter business phone' }]}
            >
              <Input placeholder="Enter business phone" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="website"
              label="Website"
            >
              <Input placeholder="Enter website URL" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="businessAddress"
          label="Business Address"
          rules={[{ required: true, message: 'Please enter business address' }]}
        >
          <TextArea rows={2} placeholder="Enter complete business address" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="taxId"
              label="Tax ID"
            >
              <Input placeholder="Enter tax identification number" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="businessLicense"
              label="Business License"
            >
              <Input placeholder="Enter business license number" />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Business Policies</Divider>

        <Form.Item
          name="returnPolicy"
          label="Return Policy"
        >
          <TextArea rows={2} placeholder="Describe your return policy" />
        </Form.Item>

        <Form.Item
          name="shippingPolicy"
          label="Shipping Policy"
        >
          <TextArea rows={2} placeholder="Describe your shipping policy" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="minimumOrderAmount"
              label="Minimum Order Amount ($)"
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="Enter minimum order amount"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="processingTime"
              label="Processing Time (days)"
            >
              <InputNumber
                min={1}
                max={30}
                style={{ width: '100%' }}
                placeholder="Enter processing time"
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Business Features</Divider>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="isActive" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Store Active</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="acceptsReturns" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Accept Returns</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="providesWarranty" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Provide Warranty</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Business Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const profileSettings = (
    <Card>
      <Form
        form={profileForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Profile', values)}
        initialValues={{
          firstName: 'John',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '******-0123',
          address: '456 Personal St, Home City, HC 67890',
          timezone: 'UTC-5',
          language: 'en',
          dateFormat: 'MM/DD/YYYY',
          currency: 'USD'
        }}
      >
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center', marginBottom: 24 }}>
            <Avatar
              size={100}
              icon={<UserOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Upload
                showUploadList={false}
                beforeUpload={() => false}
              >
                <Button icon={<CameraOutlined />} type="link">
                  Change Photo
                </Button>
              </Upload>
            </div>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="firstName"
              label="First Name"
              rules={[{ required: true, message: 'Please enter first name' }]}
            >
              <Input placeholder="Enter first name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[{ required: true, message: 'Please enter last name' }]}
            >
              <Input placeholder="Enter last name" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter email' },
                { type: 'email', message: 'Please enter valid email' }
              ]}
            >
              <Input placeholder="Enter email address" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="Phone"
              rules={[{ required: true, message: 'Please enter phone number' }]}
            >
              <Input placeholder="Enter phone number" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="address"
          label="Address"
        >
          <TextArea rows={2} placeholder="Enter your address" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="timezone"
              label="Timezone"
            >
              <Select placeholder="Select timezone">
                <Option value="UTC-8">UTC-8 (Pacific Time)</Option>
                <Option value="UTC-7">UTC-7 (Mountain Time)</Option>
                <Option value="UTC-6">UTC-6 (Central Time)</Option>
                <Option value="UTC-5">UTC-5 (Eastern Time)</Option>
                <Option value="UTC">UTC (Greenwich Mean Time)</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="language"
              label="Language"
            >
              <Select placeholder="Select language">
                <Option value="en">English</Option>
                <Option value="es">Spanish</Option>
                <Option value="fr">French</Option>
                <Option value="de">German</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="dateFormat"
              label="Date Format"
            >
              <Select placeholder="Select date format">
                <Option value="MM/DD/YYYY">MM/DD/YYYY</Option>
                <Option value="DD/MM/YYYY">DD/MM/YYYY</Option>
                <Option value="YYYY-MM-DD">YYYY-MM-DD</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="currency"
              label="Preferred Currency"
            >
              <Select placeholder="Select currency">
                <Option value="USD">USD - US Dollar</Option>
                <Option value="EUR">EUR - Euro</Option>
                <Option value="GBP">GBP - British Pound</Option>
                <Option value="CAD">CAD - Canadian Dollar</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Profile Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const notificationSettings = (
    <Card>
      <Form
        form={notificationForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Notification', values)}
        initialValues={{
          orderNotifications: true,
          paymentNotifications: true,
          reviewNotifications: true,
          promotionalEmails: false,
          weeklyReports: true,
          monthlyReports: true,
          lowStockAlerts: true,
          newMessageAlerts: true,
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: true
        }}
      >
        <Divider>Order Notifications</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="orderNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>New Order Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="paymentNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Payment Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Customer Notifications</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="reviewNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>New Review Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="newMessageAlerts" valuePropName="checked">
              <Space>
                <Switch />
                <Text>New Message Alerts</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Inventory Notifications</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="lowStockAlerts" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Low Stock Alerts</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Reports & Analytics</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="weeklyReports" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Weekly Sales Reports</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="monthlyReports" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Monthly Analytics Reports</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Marketing</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="promotionalEmails" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Promotional Emails</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Notification Methods</Divider>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="emailNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Email Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="smsNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>SMS Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="pushNotifications" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Push Notifications</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Notification Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const securitySettings = (
    <Card>
      <Form
        form={securityForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Security', values)}
        initialValues={{
          twoFactorAuth: false,
          loginAlerts: true,
          sessionTimeout: 30
        }}
      >
        <Divider>Password Security</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="currentPassword"
              label="Current Password"
            >
              <Input.Password placeholder="Enter current password" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="newPassword"
              label="New Password"
            >
              <Input.Password placeholder="Enter new password" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="confirmPassword"
              label="Confirm New Password"
            >
              <Input.Password placeholder="Confirm new password" />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Security Features</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="twoFactorAuth" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Two-Factor Authentication</Text>
              </Space>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="loginAlerts" valuePropName="checked">
              <Space>
                <Switch />
                <Text>Login Alerts</Text>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="sessionTimeout"
              label="Session Timeout (minutes)"
            >
              <InputNumber
                min={5}
                max={1440}
                style={{ width: '100%' }}
                placeholder="Enter session timeout"
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Security Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const tabItems = [
    {
      key: 'business',
      label: (
        <span>
          <ShopOutlined />
          Business Info
        </span>
      ),
      children: businessSettings,
    },
    {
      key: 'profile',
      label: (
        <span>
          <UserOutlined />
          Profile
        </span>
      ),
      children: profileSettings,
    },
    {
      key: 'notifications',
      label: (
        <span>
          <BellOutlined />
          Notifications
        </span>
      ),
      children: notificationSettings,
    },
    {
      key: 'security',
      label: (
        <span>
          <SecurityScanOutlined />
          Security
        </span>
      ),
      children: securitySettings,
    },
  ];

  return (
    <div>
      <Title level={2}>
        <SettingOutlined /> Vendor Settings
      </Title>
      <Tabs defaultActiveKey="business" items={tabItems} />
    </div>
  );
};

export default Settings;