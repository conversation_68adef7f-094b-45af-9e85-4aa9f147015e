import React from 'react';
import { 
  UserOutlined, 
  CalendarOutlined, 
  TeamOutlined, 
  EnvironmentOutlined, 
  GlobalOutlined 
} from '@ant-design/icons';
import ProfileFormField from './ProfileFormField';

const PersonalInfoSection = ({ user, userType, editData, isEditing, onInputChange }) => {
  const genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' },
    { value: 'prefer_not_to_say', label: 'Prefer not to say' }
  ];

  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    return new Date(dateString).toLocaleDateString();
  };

  if (userType === 'vendor') {
    return null; // Vendors don't have personal info section
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 flex items-center space-x-2">
          <UserOutlined className="text-orange-500" />
          <span>Personal Information</span>
        </h2>
      </div>
      <div className="px-6 py-4 space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <ProfileFormField
            label="First Name"
            value={editData.firstName}
            displayValue={user.firstName}
            isEditing={isEditing}
            onChange={(value) => onInputChange('firstName', value)}
            icon={UserOutlined}
            required
          />
          <ProfileFormField
            label="Last Name"
            value={editData.lastName}
            displayValue={user.lastName}
            isEditing={isEditing}
            onChange={(value) => onInputChange('lastName', value)}
            icon={UserOutlined}
            required
          />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <ProfileFormField
            label="Date of Birth"
            value={editData.dateOfBirth}
            displayValue={formatDate(user.dateOfBirth)}
            isEditing={isEditing}
            onChange={(value) => onInputChange('dateOfBirth', value)}
            type="date"
            icon={CalendarOutlined}
          />
          <ProfileFormField
            label="Gender"
            value={editData.gender}
            displayValue={user.gender ? user.gender.charAt(0).toUpperCase() + user.gender.slice(1).replace('_', ' ') : 'Not provided'}
            isEditing={isEditing}
            onChange={(value) => onInputChange('gender', value)}
            type="select"
            options={genderOptions}
            icon={TeamOutlined}
          />
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoSection;
