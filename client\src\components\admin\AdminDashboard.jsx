import React from 'react';
import { Layout, Card, Row, Col, Statistic, Typography, Space, Avatar, Badge } from 'antd';
import {
  UserOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  RiseOutlined,
  ArrowUpOutlined
} from '@ant-design/icons';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

const { Content } = Layout;
const { Title: AntTitle } = Typography;

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const AdminDashboard = () => {
  // Sample data for charts
  const revenueData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Revenue ($)',
        data: [12000, 19000, 15000, 25000, 22000, 30000, 28000, 35000, 32000, 40000, 38000, 45000],
        borderColor: '#1890ff',
        backgroundColor: 'rgba(24, 144, 255, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const salesByCategoryData = {
    labels: ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Beauty'],
    datasets: [
      {
        label: 'Sales',
        data: [65, 59, 80, 81, 56, 55],
        backgroundColor: [
          '#1890ff',
          '#52c41a',
          '#faad14',
          '#f5222d',
          '#722ed1',
          '#13c2c2',
        ],
      },
    ],
  };

  const orderStatusData = {
    labels: ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
    datasets: [
      {
        data: [30, 25, 20, 20, 5],
        backgroundColor: [
          '#faad14',
          '#1890ff',
          '#52c41a',
          '#13c2c2',
          '#f5222d',
        ],
      },
    ],
  };

  const userGrowthData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'New Users',
        data: [400, 600, 800, 1200, 1000, 1400],
        borderColor: '#52c41a',
        backgroundColor: 'rgba(82, 196, 26, 0.2)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px' }}>
          <AntTitle level={2} style={{ margin: 0, color: '#1890ff' }}>
            Admin Dashboard
          </AntTitle>
          <p style={{ color: '#666', marginTop: '8px' }}>
            Welcome back! Here's what's happening with your platform today.
          </p>
        </div>

        {/* Statistics Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Users"
                value={11280}
                prefix={<UserOutlined style={{ color: '#1890ff' }} />}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Active Vendors"
                value={156}
                prefix={<ShopOutlined style={{ color: '#52c41a' }} />}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Orders"
                value={2847}
                prefix={<ShoppingCartOutlined style={{ color: '#faad14' }} />}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Revenue"
                value={45280}
                prefix={<DollarOutlined style={{ color: '#f5222d' }} />}
                precision={2}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Charts Row 1 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} lg={12}>
            <Card 
              title="Revenue Trends" 
              extra={<Badge status="processing" text="Live Data" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Line data={revenueData} options={chartOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card 
              title="Sales by Category" 
              extra={<Badge status="success" text="Updated" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Bar data={salesByCategoryData} options={chartOptions} />
              </div>
            </Card>
          </Col>
        </Row>

        {/* Charts Row 2 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card 
              title="Order Status Distribution" 
              extra={<Badge status="default" text="Real-time" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Doughnut data={orderStatusData} options={doughnutOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card 
              title="User Growth Metrics" 
              extra={<Badge status="processing" text="Monthly" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Line data={userGrowthData} options={chartOptions} />
              </div>
            </Card>
          </Col>
        </Row>

        {/* Recent Activity */}
        <Row style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title="Recent Activity" extra={<a href="#">View All</a>}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Avatar icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>New user registration</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>John Doe joined the platform</div>
                  </div>
                  <div style={{ marginLeft: 'auto', color: '#666', fontSize: '12px' }}>2 min ago</div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#52c41a' }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>Vendor application approved</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>TechStore has been approved</div>
                  </div>
                  <div style={{ marginLeft: 'auto', color: '#666', fontSize: '12px' }}>5 min ago</div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Avatar icon={<ShoppingCartOutlined />} style={{ backgroundColor: '#faad14' }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>Large order placed</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>Order #12345 worth $2,500</div>
                  </div>
                  <div style={{ marginLeft: 'auto', color: '#666', fontSize: '12px' }}>10 min ago</div>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default AdminDashboard;