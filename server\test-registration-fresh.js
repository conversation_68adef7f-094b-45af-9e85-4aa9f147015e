/**
 * Test registration with fresh email addresses to avoid conflicts
 * Run with: node test-registration-fresh.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Generate unique email addresses using timestamp
const timestamp = Date.now();

const testUser = {
    firstName: 'Test',
    lastName: 'User',
    email: `test.user.${timestamp}@example.com`,
    password: 'TestPassword123',
    userType: 'user'
};

const testVendor = {
    businessName: 'Test Business',
    businessType: 'retail',
    contactPerson: 'Test Vendor',
    email: `test.vendor.${timestamp}@example.com`,
    password: 'VendorPassword123',
    userType: 'vendor'
};

async function testEndpoint(method, endpoint, data = null, headers = {}) {
    try {
        console.log(`\n🧪 Testing ${method.toUpperCase()} ${endpoint}`);
        if (data) {
            console.log('   Request data:', JSON.stringify(data, null, 2));
        }
        
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };

        if (data) {
            config.data = data;
        }

        const response = await axios(config);
        console.log(`✅ Success: ${response.status} - ${response.data.message || 'OK'}`);
        if (response.data.data) {
            console.log('   Response data keys:', Object.keys(response.data.data));
        }
        return response.data;
    } catch (error) {
        if (error.response) {
            console.log(`❌ Error: ${error.response.status} - ${error.response.data.message || 'Unknown error'}`);
            if (error.response.data.errors) {
                console.log('   Validation errors:', error.response.data.errors);
            }
            if (error.response.data.error) {
                console.log('   Error details:', error.response.data.error);
            }
            return error.response.data;
        } else {
            console.log(`❌ Network Error: ${error.message}`);
            return null;
        }
    }
}

async function testRegistrationFlow() {
    console.log('🚀 Testing Fresh Registration Flow...\n');
    console.log(`📧 Using timestamp: ${timestamp} for unique emails\n`);

    // Test 1: Health Check
    await testEndpoint('GET', '/auth/health');

    // Test 2: Fresh User Registration
    console.log('\n--- Testing User Registration ---');
    const userRegResult = await testEndpoint('POST', '/auth/register', testUser);
    let userToken = null;
    if (userRegResult && userRegResult.success) {
        userToken = userRegResult.data.token;
        console.log('   ✅ User registration successful');
        console.log('   📧 User email:', testUser.email);
    }

    // Test 3: Fresh Vendor Registration
    console.log('\n--- Testing Vendor Registration ---');
    const vendorRegResult = await testEndpoint('POST', '/auth/register', testVendor);
    let vendorToken = null;
    if (vendorRegResult && vendorRegResult.success) {
        vendorToken = vendorRegResult.data.token;
        console.log('   ✅ Vendor registration successful');
        console.log('   📧 Vendor email:', testVendor.email);
    }

    // Test 4: Duplicate Registration (should fail)
    console.log('\n--- Testing Duplicate Registration (should fail) ---');
    await testEndpoint('POST', '/auth/register', testUser);

    // Test 5: Login with new user
    if (userRegResult && userRegResult.success) {
        console.log('\n--- Testing Login with New User ---');
        await testEndpoint('POST', '/auth/login', {
            email: testUser.email,
            password: testUser.password
        });
    }

    // Test 6: Login with new vendor
    if (vendorRegResult && vendorRegResult.success) {
        console.log('\n--- Testing Login with New Vendor ---');
        await testEndpoint('POST', '/auth/login', {
            email: testVendor.email,
            password: testVendor.password
        });
    }

    // Test 7: Get Profile
    if (userToken) {
        console.log('\n--- Testing Get Profile ---');
        await testEndpoint('GET', '/auth/profile', null, {
            'Authorization': `Bearer ${userToken}`
        });
    }

    console.log('\n🎉 Registration flow tests completed!');
    console.log('\n📝 Summary:');
    console.log(`   👤 User email: ${testUser.email}`);
    console.log(`   🏢 Vendor email: ${testVendor.email}`);
    console.log('   💡 These accounts are now in your database');
}

// Test validation errors
async function testValidationErrors() {
    console.log('\n\n🔍 Testing Validation Errors...\n');

    // Test invalid email
    console.log('--- Testing Invalid Email ---');
    await testEndpoint('POST', '/auth/register', {
        firstName: 'Test',
        lastName: 'User',
        email: 'invalid-email',
        password: 'TestPassword123',
        userType: 'user'
    });

    // Test weak password
    console.log('\n--- Testing Weak Password ---');
    await testEndpoint('POST', '/auth/register', {
        firstName: 'Test',
        lastName: 'User',
        email: `weak.password.${Date.now()}@example.com`,
        password: '123',
        userType: 'user'
    });

    // Test missing required fields for vendor
    console.log('\n--- Testing Missing Vendor Fields ---');
    await testEndpoint('POST', '/auth/register', {
        email: `incomplete.vendor.${Date.now()}@example.com`,
        password: 'TestPassword123',
        userType: 'vendor'
        // Missing businessName, businessType, contactPerson
    });

    // Test invalid user type
    console.log('\n--- Testing Invalid User Type ---');
    await testEndpoint('POST', '/auth/register', {
        firstName: 'Test',
        lastName: 'User',
        email: `invalid.type.${Date.now()}@example.com`,
        password: 'TestPassword123',
        userType: 'invalid'
    });

    console.log('\n🎉 Validation tests completed!');
}

async function main() {
    try {
        // Check if server is running
        await axios.get(`${BASE_URL}/auth/health`);
        
        await testRegistrationFlow();
        await testValidationErrors();
        
    } catch (error) {
        console.log('❌ Server is not running or not accessible at http://localhost:5000');
        console.log('Please start the server with: npm run dev');
        console.log('Error details:', error.message);
    }
}

main().catch(console.error);