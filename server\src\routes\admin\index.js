const express = require('express');
const router = express.Router();

// Import route modules
const dashboardRoutes = require('./dashboard');
const usersRoutes = require('./users');
const vendorsRoutes = require('./vendors');
const ordersRoutes = require('./orders');
const productsRoutes = require('./products');
const categoriesRoutes = require('./categories');
const analyticsRoutes = require('./analytics');
const settingsRoutes = require('./settings');

// Mount routes
router.use('/dashboard', dashboardRoutes);
router.use('/users', usersRoutes);
router.use('/vendors', vendorsRoutes);
router.use('/orders', ordersRoutes);
router.use('/products', productsRoutes);
router.use('/categories', categoriesRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/settings', settingsRoutes);

module.exports = router;