const { body, param, query } = require('express-validator');
const { validateRequest } = require('../validation');

/**
 * Validation rules for product creation
 */
const validateCreateProduct = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Product name is required')
    .isLength({ min: 2, max: 200 })
    .withMessage('Product name must be between 2 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\&\.\,\'\"]+$/)
    .withMessage('Product name contains invalid characters'),

  body('description')
    .trim()
    .notEmpty()
    .withMessage('Product description is required')
    .isLength({ min: 10, max: 2000 })
    .withMessage('Product description must be between 10 and 2000 characters'),

  body('shortDescription')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Short description cannot exceed 500 characters'),

  body('category')
    .notEmpty()
    .withMessage('Category is required')
    .isMongoId()
    .withMessage('Invalid category ID'),

  body('subcategory')
    .optional()
    .isMongoId()
    .withMessage('Invalid subcategory ID'),

  body('brand')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Brand name cannot exceed 100 characters'),

  body('sku')
    .trim()
    .notEmpty()
    .withMessage('SKU is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('SKU must be between 3 and 50 characters')
    .matches(/^[A-Z0-9\-_]+$/)
    .withMessage('SKU can only contain uppercase letters, numbers, hyphens, and underscores'),

  body('barcode')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Barcode cannot exceed 50 characters'),

  // Pricing validation
  body('pricing.basePrice')
    .notEmpty()
    .withMessage('Base price is required')
    .isFloat({ min: 0 })
    .withMessage('Base price must be a positive number'),

  body('pricing.salePrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Sale price must be a positive number')
    .custom((value, { req }) => {
      if (value && req.body.pricing && parseFloat(value) >= parseFloat(req.body.pricing.basePrice)) {
        throw new Error('Sale price must be less than base price');
      }
      return true;
    }),

  body('pricing.costPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Cost price must be a positive number'),

  body('pricing.currency')
    .optional()
    .isIn(['USD', 'EUR', 'GBP', 'CAD', 'AUD'])
    .withMessage('Invalid currency'),

  body('pricing.taxClass')
    .optional()
    .isIn(['standard', 'reduced', 'zero', 'exempt'])
    .withMessage('Invalid tax class'),

  body('pricing.taxRate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Tax rate must be between 0 and 100'),

  // Inventory validation
  body('inventory.trackQuantity')
    .optional()
    .isBoolean()
    .withMessage('Track quantity must be a boolean'),

  body('inventory.quantity')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Quantity must be a non-negative integer'),

  body('inventory.lowStockThreshold')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Low stock threshold must be a non-negative integer'),

  body('inventory.allowBackorders')
    .optional()
    .isBoolean()
    .withMessage('Allow backorders must be a boolean'),

  // Specifications validation
  body('specifications.weight.value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Weight must be a positive number'),

  body('specifications.weight.unit')
    .optional()
    .isIn(['kg', 'g', 'lb', 'oz'])
    .withMessage('Invalid weight unit'),

  body('specifications.dimensions.length')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Length must be a positive number'),

  body('specifications.dimensions.width')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Width must be a positive number'),

  body('specifications.dimensions.height')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Height must be a positive number'),

  body('specifications.dimensions.unit')
    .optional()
    .isIn(['cm', 'm', 'in', 'ft'])
    .withMessage('Invalid dimension unit'),

  // Shipping validation
  body('shipping.weight')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Shipping weight must be a positive number'),

  body('shipping.shippingClass')
    .optional()
    .isIn(['standard', 'heavy', 'fragile', 'digital'])
    .withMessage('Invalid shipping class'),

  body('shipping.freeShipping')
    .optional()
    .isBoolean()
    .withMessage('Free shipping must be a boolean'),

  body('shipping.shippingCost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Shipping cost must be a positive number'),

  // SEO validation
  body('seo.metaTitle')
    .optional()
    .trim()
    .isLength({ max: 60 })
    .withMessage('Meta title cannot exceed 60 characters'),

  body('seo.metaDescription')
    .optional()
    .trim()
    .isLength({ max: 160 })
    .withMessage('Meta description cannot exceed 160 characters'),

  body('seo.keywords')
    .optional()
    .isArray()
    .withMessage('Keywords must be an array'),

  body('seo.keywords.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each keyword must be between 1 and 50 characters'),

  // Status validation
  body('status')
    .optional()
    .isIn(['draft', 'active', 'inactive', 'archived'])
    .withMessage('Invalid status'),

  body('visibility')
    .optional()
    .isIn(['public', 'private', 'password_protected'])
    .withMessage('Invalid visibility'),

  body('featured')
    .optional()
    .isBoolean()
    .withMessage('Featured must be a boolean'),

  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),

  body('tags.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 30 })
    .withMessage('Each tag must be between 1 and 30 characters'),

  // Variants validation
  body('variants')
    .optional()
    .isArray()
    .withMessage('Variants must be an array'),

  body('variants.*.name')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Variant name is required'),

  body('variants.*.options')
    .optional()
    .isArray()
    .withMessage('Variant options must be an array'),

  // Attributes validation
  body('attributes')
    .optional()
    .isArray()
    .withMessage('Attributes must be an array'),

  body('attributes.*.name')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Attribute name is required'),

  body('attributes.*.value')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Attribute value is required'),

  validateRequest
];

/**
 * Validation rules for product update
 */
const validateUpdateProduct = [
  param('id')
    .isMongoId()
    .withMessage('Invalid product ID'),

  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Product name must be between 2 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\&\.\,\'\"]+$/)
    .withMessage('Product name contains invalid characters'),

  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Product description must be between 10 and 2000 characters'),

  body('category')
    .optional()
    .isMongoId()
    .withMessage('Invalid category ID'),

  body('pricing.basePrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Base price must be a positive number'),

  body('pricing.salePrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Sale price must be a positive number'),

  body('inventory.quantity')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Quantity must be a non-negative integer'),

  body('status')
    .optional()
    .isIn(['draft', 'active', 'inactive', 'archived'])
    .withMessage('Invalid status'),

  validateRequest
];

/**
 * Validation rules for product listing
 */
const validateProductList = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('status')
    .optional()
    .isIn(['draft', 'active', 'inactive', 'archived'])
    .withMessage('Invalid status filter'),

  query('category')
    .optional()
    .isMongoId()
    .withMessage('Invalid category ID'),

  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),

  query('sortBy')
    .optional()
    .isIn(['name', 'price', 'createdAt', 'updatedAt', 'sales'])
    .withMessage('Invalid sort field'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),

  validateRequest
];

/**
 * Validation rules for product ID parameter
 */
const validateProductId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid product ID'),

  validateRequest
];

/**
 * Validation rules for bulk operations
 */
const validateBulkOperation = [
  body('productIds')
    .isArray({ min: 1 })
    .withMessage('Product IDs array is required'),

  body('productIds.*')
    .isMongoId()
    .withMessage('Invalid product ID'),

  body('action')
    .isIn(['activate', 'deactivate', 'delete', 'archive'])
    .withMessage('Invalid bulk action'),

  validateRequest
];

/**
 * Validation rules for stock update
 */
const validateStockUpdate = [
  param('id')
    .isMongoId()
    .withMessage('Invalid product ID'),

  body('quantity')
    .isInt({ min: 0 })
    .withMessage('Quantity must be a non-negative integer'),

  body('operation')
    .optional()
    .isIn(['set', 'add', 'subtract'])
    .withMessage('Invalid operation type'),

  validateRequest
];

/**
 * Custom validation for image uploads
 */
const validateProductImages = (req, res, next) => {
  // Check if images are provided
  if (!req.files || !req.files.productImages || req.files.productImages.length === 0) {
    // Images are optional for updates, required for creation
    if (req.method === 'POST' && req.path.includes('/products') && !req.path.includes('/')) {
      return res.status(400).json({
        success: false,
        message: 'At least one product image is required'
      });
    }
    return next();
  }

  const images = req.files.productImages;
  const maxImages = 8;
  const maxSize = 2 * 1024 * 1024; // 2MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];

  // Check number of images
  if (images.length > maxImages) {
    return res.status(400).json({
      success: false,
      message: `Maximum ${maxImages} images allowed`
    });
  }

  // Validate each image
  for (let i = 0; i < images.length; i++) {
    const image = images[i];

    // Check file size
    if (image.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: `Image ${i + 1} exceeds maximum size of 2MB`
      });
    }

    // Check file type
    if (!allowedTypes.includes(image.mimetype)) {
      return res.status(400).json({
        success: false,
        message: `Image ${i + 1} has invalid format. Only JPEG, PNG, and GIF are allowed`
      });
    }
  }

  next();
};

module.exports = {
  validateCreateProduct,
  validateUpdateProduct,
  validateProductList,
  validateProductId,
  validateBulkOperation,
  validateStockUpdate,
  validateProductImages
};