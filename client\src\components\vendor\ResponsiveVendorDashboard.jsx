import React from 'react';
import { Layout, Card, Row, Col, Statistic, Typography, Space, Avatar, Badge, Progress } from 'antd';
import {
  ShoppingCartOutlined,
  DollarOutlined,
  ProductOutlined,
  EyeOutlined,
  RiseOutlined,
  StarOutlined
} from '@ant-design/icons';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';
import useResponsive from '../../hooks/useResponsive';

const { Content } = Layout;
const { Title: AntTitle } = Typography;

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const ResponsiveVendorDashboard = () => {
  const { isMobile, isTablet } = useResponsive();

  // Sample data for charts
  const salesPerformanceData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Sales ($)',
        data: [3200, 4100, 3800, 5200, 4800, 6100, 5900, 7200, 6800, 8100, 7700, 9200],
        borderColor: '#52c41a',
        backgroundColor: 'rgba(82, 196, 26, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const productPerformanceData = {
    labels: ['Laptop Pro', 'Wireless Mouse', 'Keyboard', 'Monitor', 'Headphones', 'Webcam'],
    datasets: [
      {
        label: 'Units Sold',
        data: [45, 78, 62, 34, 89, 23],
        backgroundColor: [
          '#1890ff',
          '#52c41a',
          '#faad14',
          '#f5222d',
          '#722ed1',
          '#13c2c2',
        ],
      },
    ],
  };

  const revenueBreakdownData = {
    labels: ['Product Sales', 'Shipping', 'Commission', 'Returns'],
    datasets: [
      {
        data: [75, 15, 8, 2],
        backgroundColor: [
          '#52c41a',
          '#1890ff',
          '#faad14',
          '#f5222d',
        ],
      },
    ],
  };

  const topSellingProductsData = {
    labels: ['Laptop Pro', 'Wireless Mouse', 'Gaming Keyboard', 'USB-C Hub', '4K Monitor'],
    datasets: [
      {
        label: 'Revenue ($)',
        data: [12500, 8900, 7200, 5800, 4300],
        backgroundColor: '#1890ff',
        borderColor: '#1890ff',
        borderWidth: 1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            size: isMobile ? 10 : 12
          }
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      },
      x: {
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      }
    },
  };

  const horizontalBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y',
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            size: isMobile ? 10 : 12
          }
        }
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      },
      y: {
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      }
    },
  };

  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: isMobile ? 'bottom' : 'right',
        labels: {
          font: {
            size: isMobile ? 10 : 12
          }
        }
      },
    },
  };

  const cardHeight = isMobile ? '250px' : isTablet ? '300px' : '400px';
  const chartHeight = isMobile ? '150px' : isTablet ? '200px' : '300px';

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <AntTitle level={isMobile ? 3 : 2} style={{ margin: 0, color: '#52c41a' }}>
          Vendor Dashboard
        </AntTitle>
        <p style={{ color: '#666', marginTop: '8px', fontSize: isMobile ? '12px' : '14px' }}>
          Track your store performance and manage your business effectively.
        </p>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Total Sales"
              value={68420}
              prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
              precision={2}
              valueStyle={{ 
                color: '#52c41a', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
            <Progress 
              percent={85} 
              size="small" 
              strokeColor="#52c41a" 
              style={{ marginTop: '8px' }} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Orders"
              value={342}
              prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ 
                color: '#1890ff', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
            <Progress 
              percent={72} 
              size="small" 
              strokeColor="#1890ff" 
              style={{ marginTop: '8px' }} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Products"
              value={28}
              prefix={<ProductOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ 
                color: '#faad14', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
            <Progress 
              percent={60} 
              size="small" 
              strokeColor="#faad14" 
              style={{ marginTop: '8px' }} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Store Views"
              value={15680}
              prefix={<EyeOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ 
                color: '#722ed1', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
            <Progress 
              percent={90} 
              size="small" 
              strokeColor="#722ed1" 
              style={{ marginTop: '8px' }} 
            />
          </Card>
        </Col>
      </Row>

      {/* Charts Row 1 */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={12}>
          <Card 
            title="Sales Performance" 
            extra={<Badge status="processing" text={isMobile ? "Live" : "Live Data"} />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Line data={salesPerformanceData} options={chartOptions} />
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="Product Performance" 
            extra={<Badge status="success" text="Updated" />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Bar data={productPerformanceData} options={chartOptions} />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row 2 */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={12}>
          <Card 
            title="Revenue Breakdown" 
            extra={<Badge status="default" text="This Month" />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Pie data={revenueBreakdownData} options={pieOptions} />
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="Top Selling Products" 
            extra={<Badge status="processing" text="Revenue Based" />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Bar data={topSellingProductsData} options={horizontalBarOptions} />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Store Performance & Recent Orders */}
      <Row gutter={[8, 8]}>
        <Col xs={24} lg={12}>
          <Card 
            title="Store Performance" 
            extra={<a href="#">{isMobile ? "Details" : "View Details"}</a>}
            size={isMobile ? 'small' : 'default'}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Customer Rating</span>
                  <span style={{ fontWeight: 500 }}>4.8/5.0</span>
                </div>
                <Progress percent={96} strokeColor="#faad14" />
              </div>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Order Fulfillment</span>
                  <span style={{ fontWeight: 500 }}>98%</span>
                </div>
                <Progress percent={98} strokeColor="#52c41a" />
              </div>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Response Time</span>
                  <span style={{ fontWeight: 500 }}>2.3 hrs</span>
                </div>
                <Progress percent={85} strokeColor="#1890ff" />
              </div>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Return Rate</span>
                  <span style={{ fontWeight: 500 }}>2.1%</span>
                </div>
                <Progress percent={15} strokeColor="#f5222d" />
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="Recent Orders" 
            extra={<a href="#">{isMobile ? "View All" : "View All Orders"}</a>}
            size={isMobile ? 'small' : 'default'}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: isMobile ? '8px' : '12px' }}>
                <Avatar 
                  style={{ backgroundColor: '#52c41a' }} 
                  size={isMobile ? 'small' : 'default'}
                >
                  JD
                </Avatar>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500, fontSize: isMobile ? '12px' : '14px' }}>
                    Order #12345
                  </div>
                  <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                    John Doe - Laptop Pro
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div style={{ fontWeight: 500, color: '#52c41a', fontSize: isMobile ? '12px' : '14px' }}>
                    $1,299
                  </div>
                  <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                    2 min ago
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: isMobile ? '8px' : '12px' }}>
                <Avatar 
                  style={{ backgroundColor: '#1890ff' }} 
                  size={isMobile ? 'small' : 'default'}
                >
                  SM
                </Avatar>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500, fontSize: isMobile ? '12px' : '14px' }}>
                    Order #12344
                  </div>
                  <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                    Sarah Miller - Wireless Mouse
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div style={{ fontWeight: 500, color: '#52c41a', fontSize: isMobile ? '12px' : '14px' }}>
                    $89
                  </div>
                  <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                    15 min ago
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: isMobile ? '8px' : '12px' }}>
                <Avatar 
                  style={{ backgroundColor: '#faad14' }} 
                  size={isMobile ? 'small' : 'default'}
                >
                  MJ
                </Avatar>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500, fontSize: isMobile ? '12px' : '14px' }}>
                    Order #12343
                  </div>
                  <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                    Mike Johnson - Gaming Keyboard
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div style={{ fontWeight: 500, color: '#52c41a', fontSize: isMobile ? '12px' : '14px' }}>
                    $159
                  </div>
                  <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                    1 hour ago
                  </div>
                </div>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ResponsiveVendorDashboard;