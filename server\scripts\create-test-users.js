const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../schema/userSchema');

const createTestUsers = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('Connected to MongoDB');

    // Password will be hashed by the User schema pre-save middleware

    // Create Admin User
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      adminUser = new User({
        firstName: 'Test',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'password123', // Will be hashed by pre-save middleware
        userType: 'admin',
        role: 'admin',
        emailVerification: {
          isVerified: true,
          verifiedAt: new Date()
        },
        isActive: true
      });
      await adminUser.save();
      console.log('✅ Admin user created:', adminUser.email);
      console.log('   - UserType:', adminUser.userType);
      console.log('   - Role:', adminUser.role);
    } else {
      // Update existing user to ensure correct userType
      adminUser.userType = 'admin';
      adminUser.role = 'admin';
      adminUser.emailVerification.isVerified = true;
      await adminUser.save();
      console.log('✅ Admin user updated:', adminUser.email);
      console.log('   - UserType:', adminUser.userType);
      console.log('   - Role:', adminUser.role);
    }

    // Create Vendor User
    let vendorUser = await User.findOne({ email: '<EMAIL>' });
    if (!vendorUser) {
      vendorUser = new User({
        businessName: 'Test Store',
        businessType: 'retail',
        contactPerson: 'Test Vendor',
        email: '<EMAIL>',
        password: 'password123', // Will be hashed by pre-save middleware
        userType: 'vendor',
        role: 'vendor',
        businessAddress: {
          street: '123 Test Street',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345',
          country: 'Test Country'
        },
        emailVerification: {
          isVerified: true,
          verifiedAt: new Date()
        },
        vendorStatus: {
          isApproved: true,
          approvedAt: new Date()
        },
        isActive: true
      });
      await vendorUser.save();
      console.log('✅ Vendor user created:', vendorUser.email);
      console.log('   - UserType:', vendorUser.userType);
      console.log('   - Role:', vendorUser.role);
    } else {
      // Update existing user to ensure correct userType
      vendorUser.userType = 'vendor';
      vendorUser.role = 'vendor';
      vendorUser.emailVerification.isVerified = true;
      vendorUser.vendorStatus.isApproved = true;
      await vendorUser.save();
      console.log('✅ Vendor user updated:', vendorUser.email);
      console.log('   - UserType:', vendorUser.userType);
      console.log('   - Role:', vendorUser.role);
    }

    // Create Customer User
    let customerUser = await User.findOne({ email: '<EMAIL>' });
    if (!customerUser) {
      customerUser = new User({
        firstName: 'Test',
        lastName: 'Customer',
        email: '<EMAIL>',
        password: 'password123', // Will be hashed by pre-save middleware
        userType: 'user',
        role: 'customer',
        emailVerification: {
          isVerified: true,
          verifiedAt: new Date()
        },
        isActive: true
      });
      await customerUser.save();
      console.log('✅ Customer user created:', customerUser.email);
    } else {
      console.log('✅ Customer user already exists:', customerUser.email);
    }

    console.log('\n🎉 Test users created successfully!');
    console.log('\nLogin credentials:');
    console.log('Admin: <EMAIL> / password123');
    console.log('Vendor: <EMAIL> / password123');
    console.log('Customer: <EMAIL> / password123');
    console.log('\nYou can now test the admin and vendor panels!');

  } catch (error) {
    console.error('❌ Error creating test users:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the script
createTestUsers();