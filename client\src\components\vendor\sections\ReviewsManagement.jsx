import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Typography,
  Row,
  Col,
  Statistic,
  Input,
  Rate,
  Avatar,
  message,
  Divider,
  Progress
} from 'antd';
import {
  StarOutlined,
  EyeOutlined,
  SearchOutlined,
  UserOutlined,
  MessageOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const ReviewsManagement = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(false);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [selectedReview, setSelectedReview] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');
  const [replyText, setReplyText] = useState('');

  // Sample data - replace with actual API calls
  const sampleReviews = [
    {
      id: 1,
      productName: 'iPhone 15 Pro',
      productSku: 'IPH15PRO001',
      customerName: '<PERSON>',
      customerAvatar: 'https://via.placeholder.com/40x40',
      rating: 5,
      title: 'Excellent product!',
      comment: 'Amazing phone with great camera quality. Fast delivery and excellent packaging. Highly recommended!',
      date: '2024-03-20',
      helpful: 12,
      notHelpful: 1,
      verified: true,
      reply: null,
      status: 'published'
    },
    {
      id: 2,
      productName: 'AirPods Pro',
      productSku: 'AIRPODS001',
      customerName: 'Jane Smith',
      customerAvatar: 'https://via.placeholder.com/40x40',
      rating: 4,
      title: 'Good quality',
      comment: 'Sound quality is great, but the battery life could be better. Overall satisfied with the purchase.',
      date: '2024-03-18',
      helpful: 8,
      notHelpful: 2,
      verified: true,
      reply: {
        text: 'Thank you for your feedback! We appreciate your honest review.',
        date: '2024-03-19'
      },
      status: 'published'
    },
    {
      id: 3,
      productName: 'MacBook Air',
      productSku: 'MBA001',
      customerName: 'Bob Wilson',
      customerAvatar: 'https://via.placeholder.com/40x40',
      rating: 2,
      title: 'Not as expected',
      comment: 'The laptop is slower than advertised. Had some issues with the keyboard. Disappointed with this purchase.',
      date: '2024-03-15',
      helpful: 3,
      notHelpful: 8,
      verified: true,
      reply: null,
      status: 'published'
    },
    {
      id: 4,
      productName: 'iPad Pro',
      productSku: 'IPADPRO001',
      customerName: 'Alice Brown',
      customerAvatar: 'https://via.placeholder.com/40x40',
      rating: 5,
      title: 'Perfect for work',
      comment: 'Excellent tablet for professional work. The display is stunning and performance is top-notch.',
      date: '2024-03-22',
      helpful: 15,
      notHelpful: 0,
      verified: true,
      reply: null,
      status: 'published'
    },
    {
      id: 5,
      productName: 'iPhone 15 Pro',
      productSku: 'IPH15PRO001',
      customerName: 'Mike Johnson',
      customerAvatar: 'https://via.placeholder.com/40x40',
      rating: 1,
      title: 'Defective product',
      comment: 'Received a damaged phone. Screen was cracked and phone wouldn\'t turn on. Very poor packaging.',
      date: '2024-03-25',
      helpful: 2,
      notHelpful: 0,
      verified: true,
      reply: null,
      status: 'pending'
    }
  ];

  useEffect(() => {
    fetchReviews();
  }, []);

  const fetchReviews = async () => {
    setLoading(true);
    try {
      // Simulate API call
      setTimeout(() => {
        setReviews(sampleReviews);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('Failed to fetch reviews');
      setLoading(false);
    }
  };

  const handleReplyToReview = (review) => {
    setSelectedReview(review);
    setReplyText(review.reply?.text || '');
    setReplyModalVisible(true);
  };

  const handleSubmitReply = async () => {
    try {
      const updatedReviews = reviews.map(review =>
        review.id === selectedReview.id
          ? {
              ...review,
              reply: {
                text: replyText,
                date: new Date().toISOString().split('T')[0]
              }
            }
          : review
      );
      setReviews(updatedReviews);
      setReplyModalVisible(false);
      setReplyText('');
      message.success('Reply submitted successfully');
    } catch (error) {
      message.error('Failed to submit reply');
    }
  };

  const getRatingColor = (rating) => {
    if (rating >= 4) return '#52c41a';
    if (rating >= 3) return '#faad14';
    return '#f5222d';
  };

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = 
      review.productName.toLowerCase().includes(searchText.toLowerCase()) ||
      review.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      review.comment.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesRating = ratingFilter === 'all' || review.rating.toString() === ratingFilter;
    
    return matchesSearch && matchesRating;
  });

  const columns = [
    {
      title: 'Product',
      dataIndex: 'productName',
      key: 'productName',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>SKU: {record.productSku}</div>
        </div>
      ),
    },
    {
      title: 'Customer',
      dataIndex: 'customerName',
      key: 'customerName',
      render: (name, record) => (
        <Space>
          <Avatar src={record.customerAvatar} icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>{name}</div>
            {record.verified && (
              <Tag color="green" size="small">Verified Purchase</Tag>
            )}
          </div>
        </Space>
      ),
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating) => (
        <div>
          <Rate disabled defaultValue={rating} style={{ fontSize: '14px' }} />
          <div style={{ color: getRatingColor(rating), fontWeight: 500 }}>
            {rating}/5
          </div>
        </div>
      ),
      sorter: (a, b) => a.rating - b.rating,
    },
    {
      title: 'Review',
      dataIndex: 'comment',
      key: 'comment',
      render: (comment, record) => (
        <div style={{ maxWidth: 300 }}>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>{record.title}</div>
          <Paragraph
            ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
            style={{ margin: 0, fontSize: '12px' }}
          >
            {comment}
          </Paragraph>
        </div>
      ),
    },
    {
      title: 'Helpful',
      key: 'helpful',
      render: (_, record) => (
        <div>
          <div style={{ color: '#52c41a' }}>
            <LikeOutlined /> {record.helpful}
          </div>
          <div style={{ color: '#f5222d' }}>
            <DislikeOutlined /> {record.notHelpful}
          </div>
        </div>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
      title: 'Status',
      dataIndex: 'reply',
      key: 'reply',
      render: (reply) => (
        <Tag color={reply ? 'green' : 'orange'}>
          {reply ? 'Replied' : 'No Reply'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          icon={<MessageOutlined />}
          onClick={() => handleReplyToReview(record)}
        >
          {record.reply ? 'Edit Reply' : 'Reply'}
        </Button>
      ),
    },
  ];

  const totalReviews = reviews.length;
  const averageRating = reviews.length > 0 
    ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1)
    : 0;
  const repliedReviews = reviews.filter(review => review.reply).length;
  const pendingReviews = reviews.filter(review => !review.reply).length;

  // Rating distribution
  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(review => review.rating === rating).length,
    percentage: reviews.length > 0 
      ? ((reviews.filter(review => review.rating === rating).length / reviews.length) * 100).toFixed(1)
      : 0
  }));

  return (
    <div>
      <Title level={2}>Reviews Management</Title>
      
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Reviews"
              value={totalReviews}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Average Rating"
              value={averageRating}
              suffix="/ 5"
              prefix={<StarOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Replied Reviews"
              value={repliedReviews}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Pending Replies"
              value={pendingReviews}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Rating Distribution */}
      <Card title="Rating Distribution" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          {ratingDistribution.map(({ rating, count, percentage }) => (
            <Col span={24} key={rating} style={{ marginBottom: 8 }}>
              <Row align="middle">
                <Col span={2}>
                  <Text>{rating} ⭐</Text>
                </Col>
                <Col span={18}>
                  <Progress 
                    percent={parseFloat(percentage)} 
                    showInfo={false}
                    strokeColor={getRatingColor(rating)}
                  />
                </Col>
                <Col span={4} style={{ textAlign: 'right' }}>
                  <Text>{count} ({percentage}%)</Text>
                </Col>
              </Row>
            </Col>
          ))}
        </Row>
      </Card>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 16 }}>
          <Space>
            <Input
              placeholder="Search reviews..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Button
              type={ratingFilter === 'all' ? 'primary' : 'default'}
              onClick={() => setRatingFilter('all')}
            >
              All
            </Button>
            {[5, 4, 3, 2, 1].map(rating => (
              <Button
                key={rating}
                type={ratingFilter === rating.toString() ? 'primary' : 'default'}
                onClick={() => setRatingFilter(rating.toString())}
              >
                {rating} ⭐
              </Button>
            ))}
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredReviews}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} reviews`,
          }}
        />
      </Card>

      {/* Reply Modal */}
      <Modal
        title={`Reply to Review - ${selectedReview?.productName}`}
        open={replyModalVisible}
        onOk={handleSubmitReply}
        onCancel={() => setReplyModalVisible(false)}
        width={600}
      >
        {selectedReview && (
          <div>
            {/* Original Review */}
            <Card size="small" style={{ marginBottom: 16, backgroundColor: '#fafafa' }}>
              <Space>
                <Avatar src={selectedReview.customerAvatar} icon={<UserOutlined />} />
                <div>
                  <div style={{ fontWeight: 500 }}>{selectedReview.customerName}</div>
                  <Rate disabled defaultValue={selectedReview.rating} style={{ fontSize: '12px' }} />
                </div>
              </Space>
              <Divider style={{ margin: '12px 0' }} />
              <div style={{ fontWeight: 500, marginBottom: 8 }}>{selectedReview.title}</div>
              <Text>{selectedReview.comment}</Text>
            </Card>

            {/* Reply Form */}
            <div>
              <Text strong>Your Reply:</Text>
              <TextArea
                rows={4}
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Write your reply to this review..."
                style={{ marginTop: 8 }}
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ReviewsManagement;