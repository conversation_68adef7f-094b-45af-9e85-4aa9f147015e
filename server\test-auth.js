/**
 * Simple test script to verify authentication endpoints
 * Run with: node test-auth.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test data
const testUser = {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'TestPassword123',
    userType: 'user'
};

const testVendor = {
    businessName: 'Test Business',
    businessType: 'retail',
    contactPerson: 'Test Vendor',
    email: '<EMAIL>',
    password: 'VendorPassword123',
    userType: 'vendor'
};

async function testEndpoint(method, endpoint, data = null, headers = {}) {
    try {
        console.log(`\n🧪 Testing ${method.toUpperCase()} ${endpoint}`);
        if (data) {
            console.log('   Request data:', JSON.stringify(data, null, 2));
        }
        
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };

        if (data) {
            config.data = data;
        }

        const response = await axios(config);
        console.log(`✅ Success: ${response.status} - ${response.data.message || 'OK'}`);
        return response.data;
    } catch (error) {
        if (error.response) {
            console.log(`❌ Error: ${error.response.status} - ${error.response.data.message || 'Unknown error'}`);
            if (error.response.data.errors) {
                console.log('   Validation errors:', error.response.data.errors);
            }
            if (error.response.data.error) {
                console.log('   Error details:', error.response.data.error);
            }
            return error.response.data;
        } else {
            console.log(`❌ Network Error: ${error.message}`);
            return null;
        }
    }
}

async function runTests() {
    console.log('🚀 Starting Authentication API Tests...\n');

    // Test 1: Health Check
    await testEndpoint('GET', '/auth/health');

    // Test 2: User Registration
    const userRegResult = await testEndpoint('POST', '/auth/register', testUser);
    let userToken = null;
    if (userRegResult && userRegResult.success) {
        userToken = userRegResult.data.token;
    }

    // Test 3: Vendor Registration
    const vendorRegResult = await testEndpoint('POST', '/auth/register', testVendor);
    let vendorToken = null;
    if (vendorRegResult && vendorRegResult.success) {
        vendorToken = vendorRegResult.data.token;
    }

    // Test 4: User Login
    await testEndpoint('POST', '/auth/login', {
        email: testUser.email,
        password: testUser.password,
        userType: 'user'
    });

    // Test 5: Vendor Login
    await testEndpoint('POST', '/auth/login', {
        email: testVendor.email,
        password: testVendor.password,
        userType: 'vendor'
    });

    // Test 6: Invalid Login
    await testEndpoint('POST', '/auth/login', {
        email: '<EMAIL>',
        password: 'wrongpassword',
        userType: 'user'
    });

    // Test 7: Get Profile (if we have a token)
    if (userToken) {
        await testEndpoint('GET', '/auth/profile', null, {
            'Authorization': `Bearer ${userToken}`
        });
    }

    // Test 8: Forgot Password
    await testEndpoint('POST', '/auth/forgot-password', {
        email: testUser.email
    });

    // Test 9: Resend Verification
    await testEndpoint('POST', '/auth/resend-verification', {
        email: testUser.email
    });

    // Test 10: Logout (if we have a token)
    if (userToken) {
        await testEndpoint('POST', '/auth/logout', null, {
            'Authorization': `Bearer ${userToken}`
        });
    }

    console.log('\n🎉 All tests completed!');
    console.log('\n📝 Notes:');
    console.log('- Check your email for verification and password reset emails');
    console.log('- Some tests may fail if users already exist in the database');
    console.log('- Make sure your server is running on port 5000');
    console.log('- Configure SMTP settings in .env for email functionality');
}

// Check if server is running
async function checkServer() {
    try {
        // Try the main health endpoint first
        await axios.get('http://localhost:5000/api/health');
        return true;
    } catch (error) {
        try {
            // Fallback to auth health endpoint
            await axios.get(`${BASE_URL}/auth/health`);
            return true;
        } catch (authError) {
            console.log('❌ Server is not running or not accessible at http://localhost:5000');
            console.log('Please start the server with: npm run dev');
            console.log('Error details:', error.message);
            return false;
        }
    }
}

// Main execution
async function main() {
    const serverRunning = await checkServer();
    if (serverRunning) {
        await runTests();
    }
}

main().catch(console.error);